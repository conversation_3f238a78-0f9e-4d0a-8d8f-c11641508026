import os
from common.logOut import *  # ログ出力・保存
import redis
from redis.cluster import RedisCluster
from redis.exceptions import RedisError, ConnectionError, RedisClusterException


import logging
logging.basicConfig(level=logging.ERROR)

# グローバルセッション（1つだけでOK）
session = None


# 2.MemoryDB接続

# MemoryDB Redisクライアント取得クラス
class MemoryDBSession:

    def __init__(self):
        self.MEMORYDB_HOST = os.environ.get("MEMORYDB_HOST", "None")
        self.MEMORYDB_PORT = os.environ.get("MEMORYDB_PORT", "None")
        self.MEMORYDB_PASSWORD = os.environ.get("MEMORYDB_PASSWORD", "None")
        self.redis_client = None

        # print(f"MemoryDBSession: MEMORYDB_HOST:{self.MEMORYDB_HOST} MEMORYDB_PORT:{self.MEMORYDB_PORT} MEMORYDB_PASSWORD:{self.MEMORYDB_PASSWORD}")

    def connect(self):
        # print(f"MemoryDBSession.connect 実行")
        try:
            # raise Exception("その他")
            self.redis_client = redis.Redis(
                host=self.MEMORYDB_HOST,
                port=self.MEMORYDB_PORT,
                password=self.MEMORYDB_PASSWORD,
                # ssl=True,
                decode_responses=False,  # ← バイナリモードで統一
                socket_timeout=5,         # ← タイムアウト秒を明示
                socket_connect_timeout=5  # ← 接続時のタイムアウト秒
            )
            return self

        except (Exception, ConnectionError, TimeoutError) as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.REDIS_NO_DATA,
                       f"MemoryDBSession: MEMORYDB_HOST:{self.MEMORYDB_HOST} MEMORYDB_PORT:{self.MEMORYDB_PORT} {e}")
            logging.error(
                f"MemoryDBSession: MEMORYDB_HOST:{self.MEMORYDB_HOST} MEMORYDB_PORT:{self.MEMORYDB_PORT} {e}")
            self.redis_client = None
            raise Exception("その他")

    def get_redis_client(self):
        # print(f"MemoryDBSession.get_redis_client 実行")
        global session
        if session is None:
            session = self.connect()
        return session.redis_client
