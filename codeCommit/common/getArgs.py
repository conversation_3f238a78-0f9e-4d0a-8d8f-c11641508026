# from common.splitPid import *                           # パーセルID分解
# 自作ライブラリ
from common.logOut import *                             # ログ出力・保存

# 1.入力パラメータ／URL解析


class getArgs:

    @staticmethod
    def validate(event):
        path = event.get('rawPath', '')
        query = event.get('queryStringParameters', {}) or {}
        fmtver = query.get("fmtver", "")

        params = {
            "rawPath": path,
            "cid": query.get("cid", None),
            "sid": query.get("sid", None),
            "aid": query.get("aid", None),
            "fmtver": fmtver
        }

        if None == params["cid"] or None == params["sid"] or None == params["aid"]:
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA,
                       f"getArgs : cid[{params["cid"]}], sid[{params["sid"]}], aid[{params["aid"]}]")
            raise ValueError("認証情報不足")

        # データがすべてセットされているかチェック
        missing = [name for name, val in [('cid', params["cid"]), ('sid', params["sid"]), (
            'aid', params["aid"]), ('fmtver', params["fmtver"])] if not val]
        if missing:
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA,
                       f"getArgs: : {', '.join(missing)}[None]")
            raise ValueError("世代番号用コードが取得できない")

        try:
            # 正規表現を使用した場合
            import re
            path_only = path.split("?", 1)[0]
            match = re.search(r"(?:.*/)?([^/]+)/([^/]+)$", path_only)

        except Exception as e:
            # 不明なエラー
            logOut.log(LOG_LEVEL_ERROR, 500,
                       LogCodes.BASE_EXCEPTION, f"getArgs: {e}")
            raise Exception("その他")

        if match:
            params["data_code"] = match.group(1)
            params["cgi_name"] = match.group(2)
        else:
            params["data_code"] = ""
            params["cgi_name"] = ""

        if not params["data_code"]:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_NO_DATA,
                       f"getArgs: 世代番号用コードが取得できない")
            raise ValueError("世代番号用コードが取得できない")

        # # getmapdata.cgiの時有効にする
        # if "getmapdata.cgi" == params["cgi_name"]:
        #     # pidがあった場合
        #     pid = query.get("pid", None)

        #     if None == pid or 0 == len(pid):
        #         logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA, f"getArgs: pid[{pid}]")
        #         raise ValueError("pidデータエラー")
        #     splitPid_param = splitPid.validate(pid)
        #     params.update(splitPid_param)

        return params
