# 自作ライブラリ
from common.logOut import *                             # ログ出力・保存

# 7.パーセルID分解

class splitPid:
    @staticmethod
    def validate(pid) -> dict:
        # pid が "0_36_27_408_-1" のような4もしくは5つの整数からなる形式かをチェック
        try:
            parts = pid.split("_")
        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION, f"splitPid: {e}")
            raise Exception("想定外例外発生")

        if ( len(parts) != 4 and len(parts) != 5 ):
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: パーセルID-フォーマット形式不正 [{pid}]")
            raise ValueError(f"パーセルIDのフォーマット形式不正: {pid}")

        # 数値（"-1" も含む）で構成されているか
        for i in range(0, len(parts)):
            try:
                ipart = int(parts[i])
            except Exception as e:
                logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: {e}")
                raise ValueError("フォーマット異常")

            if( i==0 ):
                ilevel = ipart
                if( (ipart < -1) or (6 < ipart) ):
                    logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: パーセルID-レベル番号不正 [{ipart}]")
                    raise ValueError("フォーマット異常")

            elif( i==1 ):
                iblockSetNo = ipart
                if( (ipart < 0) or (63 < ipart) ):
                    logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: パーセルID-ブロックセット番号不正 [{ipart}]")
                    raise ValueError("フォーマット異常")

            elif( i==2 ):
                blockNo = ipart
                if (ipart < 0) or (1023 < ipart) :
                    logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: パーセルID-ブロック番号不正 [{ipart}]")
                    raise ValueError("フォーマット異常")

            elif( i==3 ):
                iparcelNo = ipart
                if( (ipart < 0) or (1023 < ipart) ):
                    logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"splitPid: パーセルID-パーセル番号不正 [{ipart}]")
                    raise ValueError("フォーマット異常")

        if len(parts) == 4 :
            params = {
                "levelNo": parts[0],    # レベル番号
                "blockSetNo": parts[1], # ブロックセット番号
                "blockNo": parts[2],    # ブロック番号
                "parcelNo": parts[3],   # パーセル番号
                "divNo": "-1"         # 分割番号
            }
        elif len(parts) == 5 :
            params = {
                "levelNo": parts[0],    # レベル番号
                "blockSetNo": parts[1], # ブロックセット番号
                "blockNo": parts[2],    # ブロック番号
                "parcelNo": parts[3],   # パーセル番号
                "divNo": parts[4]       # 分割番号
            }

        return params
