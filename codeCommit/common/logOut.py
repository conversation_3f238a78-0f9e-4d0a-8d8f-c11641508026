import os
import platform
import threading
import inspect
import json
import sys
import pytz
from datetime import datetime
from typing import Optional

# ログレベル定義
LOG_LEVEL_ERROR = "ERROR"
LOG_LEVEL_WARNING = "WARNING"
# Firehoseの配信ストリーム名を環境変数から取得
FIREHOSE_DELIVERY_STREAM_NAME = os.environ.get('FIREHOSE_STREAM_NAME', '')
# ログ出力モード: "firelens" (stdout経由でFluentBit) または "direct" (boto3直接)
LOG_OUTPUT_MODE = os.environ.get('LOG_OUTPUT_MODE', 'firelens')


class DebugLogger:
    def __init__(self, enabled=True):
        self.enabled = enabled

    def print(self, msg: str):
        if self.enabled:
            f = inspect.currentframe().f_back
            print(f"[DEBUG] {f.f_code.co_name}:{f.f_lineno} - {msg}")

# ログレベル情報保持用の構造体的クラス


class LogLevelMess:
    def __init__(self,  message: str):
        self.message = message

# ログレベル定義


class LogLebel:
    ERROR = LogLevelMess("ERROR")
    WARNING = LogLevelMess("WARNING")

# ログ情報保持用の構造体的クラス


class LogContext:
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

# ログ出力定義一覧（共通で使えるコードとメッセージ）


class LogCodes:
    # 0x 基本処理
    # BASE_UNKNOWN            = LogContext("00", "不明なエラー")
    BASE_CONNECTION_FAILED = LogContext("01", "接続失敗")
    BASE_NO_DATA = LogContext("02", "データなし")
    BASE_INVALID_DATA = LogContext("03", "データ不正")
    BASE_EXCEPTION = LogContext("99", "例外発生")
    # 1x AuroraDB PostgreSQL
    PG_UNKNOWN = LogContext("10", "不明なエラー")
    PG_CONNECTION_FAILED = LogContext("11", "接続失敗")
    PG_NO_DATA = LogContext("12", "データなし")
    PG_EXCEPTION = LogContext("19", "例外発生")
    # 2x MemoryDB
    REDIS_UNKNOWN = LogContext("20", "不明なエラー")
    REDIS_CONNECTION_FAILED = LogContext("21", "接続失敗")
    REDIS_NO_DATA = LogContext("22", "データなし")
    REDIS_EXCEPTION = LogContext("29", "例外発生")
    # 3x ファイル関連
    FILE_UNKNOWN = LogContext("30", "不明なエラー")
    FILE_NOT_FOUND = LogContext("31", "接続失敗")
    FILE_READ_ERROR = LogContext("32", "データなし")
    FILE_EXCEPTION = LogContext("39", "例外発生")


# (6)ログ出力

class logOut:
    # イベント情報取得用
    _event: Optional[dict] = None

    # イベント情報取得
    @classmethod
    def set_event(cls, event: dict):
        cls._event = event

    @staticmethod
    def debug(msg: str):
        print(f"DEBUG::000::debug::{msg}")

    @classmethod
    def log(cls, error_level: str, http_status: int, context, detail: str = ""):

        # イベント情報取得
        event = cls._event or {}

        timestamp = datetime.now(pytz.timezone(
            "Asia/Tokyo")).strftime("%Y/%m/%d %H:%M:%S.%f")[:-3]

        # # フルパス情報取得
        headers = event.get("headers", {})
        protocol = headers.get("x-forwarded-proto", "https")
        host = headers.get("host", "unknown-host")
        request_context = event.get("requestContext", {}).get("http", {})
        path = request_context.get("path", "/")
        query = event.get("rawQueryString", "")
        # フルパス
        full_url = f"{protocol}://{host}{path}"
        if query:
            full_url += f"?{query}"

        # cgi名
        cgi_name = path.strip("/").split("/")[-1] or "unknown_cgi"

        # ログ構築（テキスト形式）
        log_text = (
            f"{timestamp}"
            f":{error_level}"
            f":{http_status}"
            f":{context.code}:{context.message}"
            f":{detail}"
            f":{full_url}"
        )

        # ログ構築（JSON形式 - Firehose/分析用）
        log_json = {
            "timestamp": timestamp,
            "level": error_level,
            "http_status": http_status,
            "error_code": context.code,
            "error_message": context.message,
            "detail": detail,
            "url": full_url,
            "path": path,
            "cgi_name": cgi_name
        }

        try:
            if LOG_OUTPUT_MODE == 'firelens':
                # FireLens (Fluent Bit) 経由でFirehoseに送信
                # stdout に JSON 形式で出力 → Fluent Bit が収集 → Firehose へ
                print(json.dumps(log_json, ensure_ascii=False), file=sys.stdout, flush=True)
            else:
                # 従来の直接送信方式（Lambda用）
                import boto3
                firehose_client = boto3.client('firehose')
                firehose_client.put_record(
                    DeliveryStreamName=FIREHOSE_DELIVERY_STREAM_NAME,
                    Record={'Data': log_text + "\n"}
                )

        except Exception as e:
            # ログ送信失敗時は stderr に出力（Fluent Bit が収集）
            print(f"Log送信失敗: {log_text} | Error: {e}", file=sys.stderr, flush=True)
            # logOut.log() で例外が外に出てはいけません
