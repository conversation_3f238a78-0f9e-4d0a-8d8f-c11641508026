import struct
from common.logOut import *

# 6.fmtver分解

# フォーマットバージョン文字列を分解し、バイナリ値として取得するクラス
class convFmtver:
    def __init__(self, fmtver_str: str):
        self.major = 0
        self.minor = 0
        self.revision = 0
        self.version = 0

        parts = fmtver_str.split('_')
        if len(parts) != 3:
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"convFmtver: フォーマット異常")
            raise ValueError("フォーマット異常")

        try:
            self.major = int(parts[0])
            self.minor = int(parts[1])
            self.revision = int(parts[2])
        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, f"convFmtver: {e}")
            raise ValueError("フォーマット異常")

        # ビット長チェック（1,1,2バイト）
        if not (0 <= self.major <= 0xFF) or not (0 <= self.minor <= 0xFF) or not (0 <= self.revision <= 0xFFFF):
            msg = f"convFmtver: 値が超えています: major=" + str(self.major) + ", minor=" + str(self.minor) + ", version=" +str(self.revision)
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_INVALID_DATA, msg)
            raise ValueError(msg)

        self.version = (self.major << 24) + (self.minor << 16) + self.revision


    def check(self,fmtver_str: str):
        parts = fmtver_str.split('_')
        if len(parts) != 3:
            return False
        return True

    def to_uint(self):
        return self.version

    def to_bytes(self, success: bool = True):
        """ ヘッダ部用の8バイトバイナリに変換 (4バイトversion + 4バイトstatus) """
        version_bytes = struct.pack('>I', self.to_uint())
        status_bytes = struct.pack('>I', 0x00000000 if success else 0x00000001)
        return version_bytes + status_bytes


