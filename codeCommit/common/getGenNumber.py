# # AWS SDK関連ライブラリ
from typing import Tuple, Union

# 自作ライブラリ
from common.logOut import *                             # ログ出力・保存

# (2) データ世代取得

class getGenNumber:
    @staticmethod
    def resolve(redis_client, data_code: str) -> Union[str, bytes, None]:
        value = None
        if not data_code:
            return ""

        # キーの生成
        RandumKey = "R-"+data_code

        try:
            value = redis_client.get(RandumKey)
        except Exception as e:
            # redis_clientで問題が発生した場合
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION, f"RandumKey:{RandumKey} Value:{value} {e}")
            raise Exception("世代番号取得失敗")

        if (None == value) or 0 == len(value):
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.REDIS_NO_DATA, f"RandumKey:{RandumKey} Value:{value}")
            raise Exception("世代番号が取得できない")

        value = value.decode("utf-8") if value else ""

        return value

