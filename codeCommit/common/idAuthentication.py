import redis
from common.logOut import *

# 4.ID認証


class idAuthentication:
    @staticmethod
    def verify(redis_client, auth_key: str, cid: str, sid: str, aid: str) -> int:

        # 認証対象のデータを作成
        target = f"{cid},{sid},{aid}"
        value = None

        # MemoryDBから認証情報取得
        try:
            value = redis_client.get(auth_key)
        except Exception as e:
            # データなし
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                       f"idAuthentication: Key[{auth_key}] Value[{value}]:[{target}] {e}")
            raise Exception("認証情報取得失敗")

        # 認証情報比較
        if value is None or 0 == len(value):
            # 例外発生
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_NO_DATA,
                       f"idAuthentication: Key[{auth_key}] Value[{value}]:[{target}] 認証その他エラー")
            raise Exception(f"認証その他エラー")

        value = value.decode("utf-8") if value else ""
        # print(f"認証 auth_key[{auth_key}] value[{value}]")

        decoded = value.decode(
            "utf-8") if isinstance(value, bytes) else str(value)
        # 複数ある場合
        records = decoded.split(";")

        # 認証文字列が含まれているか判定
        if target in records:
            # 認証成功時の処理
            return 0
        else:
            # 認証失敗時の処理
            logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA,
                       f"idAuthentication: Key[{auth_key}] Value[{value}]:[{target}] 認証失敗")
            raise ValueError(f"認証失敗")
