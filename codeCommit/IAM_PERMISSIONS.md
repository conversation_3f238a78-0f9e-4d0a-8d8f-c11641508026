# Required IAM Permissions for Firehose Logging

## Task Role: `ecs-dev-jzmmap-role`

The ECS task role needs the following permissions to send logs to Firehose via Fluent Bit:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "firehose:PutRecord",
                "firehose:PutRecordBatch"
            ],
            "Resource": "arn:aws:firehose:ap-northeast-1:242201295144:deliverystream/dev-aws-jzmmap-knf"
        }
    ]
}
```

## Execution Role: `ecsTaskExecutionRole`

The execution role should already have permissions to pull images and write to CloudWatch Logs.
Verify it includes:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
            "Resource": "*"
        }
    ]
}
```

## How to Apply

1. Go to AWS IAM Console
2. Find role: `ecs-dev-jzmmap-role`
3. Add the Firehose permissions policy
4. Verify the role ARN matches: `arn:aws:iam::242201295144:role/ecs-dev-jzmmap-role`

## Verification

After deployment, check:
- CloudWatch Logs group: `/ecs/dev-aws-jzmmap-ecs-task`
- Firehose delivery stream: `dev-aws-jzmmap-knf`
- S3 bucket (Firehose destination) for log files

