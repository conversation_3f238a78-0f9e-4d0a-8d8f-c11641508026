# /app/app.py
from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse
import base64
import os
import getmapdata
import db_config


# --- 環境変数デフォルト設定（未設定時のみ） ---
# MEMORYDB_PASSWORD は (None) 指定のため未設定のままか空文字。
_required_env_defaults = {
    # "POSTGRES_HOST": "localhiost",
    # "POSTGRES_PORT": "5432",
    # "POSTGRES_DB": "zm3middle",
    # "POSTGRES_USER": "jzmuser",
    # "POSTGRES_PASSWORD": "jzm",
    # "MEMORYDB_HOST": "clustercfg.dev-aws-jzmmapcache.eu8cqt.memorydb.ap-northeast-1.amazonaws.com",
    "MEMORYDB_HOST": "localhost",
    "MEMORYDB_PORT": "6379",
    "MEMORYDB_PASSWORD": "",  # パスワード使用時にコメント解除し設定
    # "FIREHOSE_STREAM_NAME": "dev-aws-jzmmap-knf",
}
for _k, _v in _required_env_defaults.items():
    os.environ.setdefault(_k, _v)
# ------------------------------------------------

app = FastAPI()


async def build_apigw_event(request: Request, path_params: dict | None = None) -> dict:
    """API Gateway (REST) 互換 event を生成し、クエリ文字列をそのまま渡す。
    - 同名パラメータ: multiValueQueryStringParameters に全値
    - 最後の値: queryStringParameters に単一値 (API GW 仕様準拠)
    - 元のクエリ文字列: rawQueryString に格納（順序/エンコード維持）
    """
    qp = request.query_params  # starlette QueryParams
    query_single = {k: qp.getlist(k)[-1] for k in qp.keys()}
    query_multi = {k: qp.getlist(k) for k in qp.keys()}
    raw_query = request.url.query  # 生のクエリ（%エンコード/順序維持）

    headers = {k: v for k, v in request.headers.items()}
    raw_body = await request.body()

    is_b64 = False
    body_str = ""
    if raw_body:
        try:
            body_str = raw_body.decode("utf-8")
        except UnicodeDecodeError:
            body_str = base64.b64encode(raw_body).decode("ascii")
            is_b64 = True

    path_only = request.url.path
    path_with_query = f"{path_only}?{raw_query}" if raw_query else path_only

    event = {
        "resource": path_only,
        "path": path_only,
        "rawPath": path_only,
        "httpMethod": request.method,
        "headers": headers,
        "multiValueHeaders": {},
        "queryStringParameters": query_single,
        "multiValueQueryStringParameters": query_multi,
        "rawQueryString": raw_query,  # API GW 仕様 (クエリ部のみ)
        "rawPathWithQuery": path_with_query,  # 追加: パス+クエリ全部
        # 要求: request にはクエリ部のみ (?付き) を渡す
        "request": f"?{raw_query}" if raw_query else "",
        "pathParameters": path_params or {},
        "stageVariables": {},
        "requestContext": {
            "resourcePath": path_only,
            "httpMethod": request.method,
            "path": path_only,
            "http": {  # logOut が参照する想定のフィールドを追加
                "method": request.method,
                "path": path_only,
            },
        },
        "body": body_str,
        "isBase64Encoded": is_b64,
    }
    # data_code を path から抽出し追加（保険）
    try:
        parts = [p for p in path_only.split('/') if p]
        if len(parts) >= 2:
            event.setdefault('data_code', parts[-2])
    except Exception:
        pass
    return event


def to_fastapi_response(result: dict) -> Response:
    status = result.get("statusCode", 200)
    headers = result.get("headers") or {}
    body = result.get("body", "")
    is_b64 = result.get("isBase64Encoded", False)
    content_type = headers.get("Content-Type", None)

    if is_b64:
        content = base64.b64decode(body) if isinstance(body, str) else body
        return Response(content=content, status_code=status, headers=headers, media_type=content_type)
    else:
        # 文字列ボディをそのまま返却（JSON文字列もそのまま）
        text = body if isinstance(body, str) else str(body)
        return Response(content=text, status_code=status, headers=headers, media_type=content_type or "text/plain; charset=utf-8")


@app.api_route("/sample30/getmapdata.cgi", methods=["GET", "POST"])
async def route_getmapdata(request: Request):
    try:
        event = await build_apigw_event(request)
        result = getmapdata.lambda_handler(event, None)
        return to_fastapi_response(result)
    except Exception:
        return JSONResponse({"message": "internal error"}, status_code=500)


@app.get("/index.html")
def healthz():
    return {"ok": True}


@app.get("/")
def root():
    return {"message": "Welcome to the AWS_getmapdata FastAPI application."}


@app.get("/dbtest")
def db_test():
    """DB接続テスト用エンドポイント"""
    # Example query:
    # "SELECT count(*) FROM schema20250930.tbl_parcel_data;"
    pg_conn = db_config.get_postgres_connection()
    db_config.test_psql_connection(pg_conn)
    # cur = pg_conn.cursor()
    # cur.execute(
    #     "SELECT count(*) FROM schema20250930.tbl_parcel_data;")
    # res = cur.fetchone()
    pg_conn.close()
    redisdb = db_config.test_redis_connection(db_config.connectRedis())
    return {"message": f"DB test endpoint - Redis DB size: {redisdb}"}
