{"executionRoleArn": "arn:aws:iam::242201295144:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::242201295144:role/ecs-dev-jzmmap-role", "containerDefinitions": [{"name": "dev-aws-jzmmap-ecs-container", "image": "<IMAGE1_NAME>", "environment": [{"name": "FIREHOSE_STREAM_NAME", "value": "dev-aws-jzmmap-knf"}, {"name": "AWS_REGION", "value": "ap-northeast-1"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "region": "ap-northeast-1", "delivery_stream": "dev-aws-jzmmap-knf"}}, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "dependsOn": [{"containerName": "log_router", "condition": "START"}]}, {"name": "log_router", "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:stable", "essential": true, "firelensConfiguration": {"type": "fluentbit", "options": {"enable-ecs-log-metadata": "true"}}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dev-aws-jzmmap-ecs-task", "awslogs-region": "ap-northeast-1", "awslogs-stream-prefix": "firelens"}}, "memoryReservation": 50}], "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "1024", "memory": "2048", "family": "dev-aws-jzmmap-ecs-task", "tags": [{"key": "Name", "value": "dev-aws-jzmmap-ecs-task"}, {"key": "Group", "value": "dev-aws-jzmmap-ecs-task"}, {"key": "Service", "value": "dev-jzm"}]}