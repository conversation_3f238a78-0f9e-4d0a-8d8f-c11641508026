FROM public.ecr.aws/docker/library/python:3.12-slim
#FROM python:3.12-slim

# システム基本ツール（必要に応じて）とタイムゾーン設定は任意
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV MEMORYDB_HOST="clustercfg.dev-aws-jzmmapcache.eu8cqt.memorydb.ap-northeast-1.amazonaws.com"
ENV MEMORYDB_PORT="6379"
# パスワード使用時にコメント解除し設定
ENV MEMORYDB_PASSWORD=""  
ENV FIREHOSE_STREAM_NAME="dev-aws-jzmmap-knf"

WORKDIR /app

# 依存ファイルを先にコピーしてレイヤーキャッシュを活用
COPY requirements.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# アプリ本体をコピー（直下の .py と common ディレクトリを含む）
COPY app.py /app/
COPY getmapdata.py /app/getmapdata.py
COPY getparcelmngdata.py /app/getparcelmngdata.py
COPY getparcelid.py /app/getparcelid.py
COPY common /app/common

# Flask アプリを 0.0.0.0:80 で待ち受ける
EXPOSE 80

# 本番運用では gunicorn + gthread/uvicorn など推奨
CMD ["gunicorn", "-k", "uvicorn.workers.UvicornWorker", "-w", "4", "-b", "0.0.0.0:80", "app:app"]
