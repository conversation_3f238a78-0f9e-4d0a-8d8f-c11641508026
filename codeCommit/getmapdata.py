import json
import os
import redis
import traceback
import time
import struct
import threading
import boto3
import base64
import math
import inspect
import concurrent.futures


# AWS SDK関連ライブラリ
import botocore.session
from botocore.model import ServiceId
from botocore.signers import RequestSigner
from cachetools import TTL<PERSON>ache, cached
from typing import Tuple, Union
from urllib.parse import Pa<PERSON><PERSON><PERSON><PERSON>, urlencode, urlunparse


# 自作ライブラリ
from common.getGenNumber import getGenNumber          # 世代番号取得
from common.getArgs import getArgs                    # 入力パラメータ解析
from common.idAuthentication import idAuthentication  # ID認証
from common.convFmtver import convFmtver              # fmtver分解
from common.logOut import *                             # ログ出力・保存
from common.MemoryDBSession import MemoryDBSession     # MemoryDB Redisクライアント取得クラス
from common.splitPid import *                           # パーセルID分解


# デバッグ出力
def debug_print(msg: str):
    f = inspect.currentframe().f_back
    print(f"[DEBUG] {f.f_code.co_name}:{f.f_lineno} - {msg}")


# エラーコードと、メッセージ定義用クラス
class ErrorContext:
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

# エラー定義一覧（共通で使えるコードとメッセージ）


class ErrorCodes:
    REDIS_CONNECTION_FAILED = ErrorContext(500, "Redis接続に失敗しました")
    AUTH_FAILURE = ErrorContext(400, "認証失敗")
    GENERATION_NOT_FOUND = ErrorContext(400, "世代番号が見つかりません")
    INTERMEDIATE_DATA_NOT_FOUND = ErrorContext(400, "中間データが見つかりません")
    FORMAT_VERSION_INVALID = ErrorContext(400, "フォーマットバージョン形式が不正です")
    PARAMETER_MISSING = ErrorContext(400, "パラメータが不足しています")
    INTERNAL_SERVER_ERROR = ErrorContext(500, "内部エラー発生しました")
    BAD_REQUEST = ErrorContext(400, "リクエストエラー")


# (3)中間データ抽出
def DataFetcherBuilder(redis_client, params: dict) -> Union[dict, bytes, None]:
    # --- マクロ風の定数宣言（bytes） ---
    ZERO4 = (0).to_bytes(4,  byteorder="big")   # 4バイトの空データ
    ZERO16 = (0).to_bytes(16, byteorder="big")   # 16バイトの空データ

    # 種別データ保存結果確認用
    # 保存領域初期化
    kinds_data: list[tuple[str, bytes]] = []

    try:
        # 種別：0～5
        kinds = ["00", "01", "02", "03", "04", "05"]
        print(f"DataFetcherBuilder kinds初期値:{kinds}")
        # Phase2：kind=6が指定されたら"06"を追加
        if "6" == params["kind"]:
            kinds.append("06")

        # Phase3：kind=7が指定されたら"07"を追加
        # kind=7はD2D規制情報データ
        if "7" == params["kind"]:
            kinds.append("07")
            print("kind=7指定検出")
        # Phase3：kind=8が指定されたら"08"を追加
        # kind=8は大型車規制情報データ
        if "8" == params["kind"]:
            kinds.append("08")

        # シーケンシャル取得して、ヒットしたものだけ上書き
        for kind in kinds:
            # キー作成
            # レベル、ブロックセット、ブロック、パーセル、分割、データ種別、世代番号
            pid_key = (
                params["levelNo"].zfill(2)
                + params["blockSetNo"].zfill(4)
                + params["blockNo"].zfill(4)
                + params["parcelNo"].zfill(4)
                + params["divNo"].zfill(4)
                + kind.zfill(3)
                + params["generation"].zfill(2)
            )

            # MemoryDB から pid_key でデータ抽出
            pid_value = redis_client.get(pid_key)

            # 結果判定
            if pid_value is not None:
                # 抽出成功
                value = pid_value   # 取得結果を採用
            else:
                # 抽出できなかった
                # Redisに無いときは定義済みのゼロデータを使う
                # kindが"00"(主要地図データ)は、16バイト分初期化
                # kindが"00"以外は4バイト分初期化
                # 道路データ、背景データ、名称データ、路線番号データ、行政界面データ、全道路標高データ
                value = ZERO16 if kind == "00" else ZERO4

            # データ蓄積
            kinds_data.append((kind, value))

    except Exception as e:
        logOut.log(LogLebel.ERROR, 500, LogCodes.REDIS_NO_DATA,
                   f"中間データ取得失敗:pid_key: {e}")
        raise Exception("その他")

    return kinds_data

# 数字を数値変換して指定のバイト数にする


def get_byte_hex(value_str: str, byte_len: int) -> bytes:
    value_int = int(value_str)
    if value_int == -1:
        return b'\xFF' * byte_len
    return value_int.to_bytes(byte_len, byteorder='big')

# __init__で値を返すことができないので、新規に作成した例外でキャッチする
# ResponseMetadataBuilderの生成に失敗した場合にスローされる例外


class ResponseMetadataBuilderError(Exception):
    pass

# 出力パラメータ生成クラス
# CGI毎に処理内容が頃なるため個別クラスとする


class ResponseMetadataBuilder:
    def __init__(self, redis_client, params: dict, fmtver: str):
        # フォーマットバージョン文字列を分解し、バイナリ値として取得
        self.ver = convFmtver(fmtver)
        self.redis_client = redis_client
        self.intermediate_value = []
        self.params = params
        self.header = b""           #
        self.percel_head = b""      # bytes型で初期化
        self.percel_id = b""        # bytes型で初期化
        self.percel_data = b""      # bytes型で初期化
        self.percel_size = 0        # パーセルのサイズ
        self.percel_data_num = 0    # パーセル単位データ管理数
        self.divNum = 0             # 分割数

        try:
            # パーセル分割情報変換
            Ret = self.splitDivInfo()

        except Exception as e:
            # logOut.log(LogLebel.ERROR, 500, LogCodes.REDIS_NO_DATA, f"出力パラメータ生成:データ作成: {e}")
            raise Exception("データ作成失敗")

        if True != Ret:
            logOut.log(LogLebel.ERROR, 400,
                       LogCodes.REDIS_NO_DATA, f"出力パラメータ生成:データなし")
            raise ResponseMetadataBuilderError("データなし")

    def set_intermediate_value(self, intermediate_value: dict):
        # 中間データ:dict型(kind,value)
        self.intermediate_value = intermediate_value

    # グループ管理情報数[0002]+Reserved[0000]
    # ヘッダ取得
    def get_header(self) -> bytes:
        return self.header

    # パーセルヘッダ取得
    def get_percel_head(self) -> bytes:
        return self.percel_head

    # パーセルID取得
    def get_percel_id(self) -> bytes:
        return self.percel_id

    # パーセルデータ取得
    def get_percel_data(self) -> bytes:
        return self.percel_data

    # 分割数取得
    def get_divNum(self) -> int:
        return self.divNum

    # グループ管理情報数[0002]+Reserved[0000]
    def make_header(self):

        # (4)出力パラメータ加工
        #  1)ヘッダ部にフォーマット・バージョン設定（入力パラメータfmtver：3.0.00）
        # header = version(4byte) + status(4byte, 0固定)
        # '>II' は「ビッグエンディアンの4バイト整数 ×2」
        header_bytes = struct.pack('>II', self.ver.version, 0)

        #  2)「グループ管理情報数」に2（固定値）を設定
        # グループ管理情報数[0002]
        group_num = (0x0002).to_bytes(2, byteorder="big")

        #  3)「パーセル情報」に、1（固定値）を設定
        percel_num = (0x0001).to_bytes(2, byteorder="big")

        # 4)「グループ管理情報」に、(3)1)抽出データを設定
        # (3)1) データ種別(kind)=0000(グループ管理情報)，サブ種別(sub_kind)=0002/0003，世代番号でMemoryDBから抽出
        sub_groups = (("2", "0002"), ("3", "0003"))
        group_manage_data = bytearray()  # 抽出データ保存用

        for label, sub_kind in sub_groups:
            # キー生成
            group_manage_key = f"0000{sub_kind}{self.params['generation']}"

            try:
                value = self.redis_client.get(group_manage_key)

            except Exception as e:
                logOut.log(LogLebel.ERROR, 500, LogCodes.REDIS_NO_DATA,
                           f"中間データ取得失敗:グループ管理情報:key=[{group_manage_key}]: {e}")
                raise Exception("その他")

            # 値なし（None / 空）チェック → クライアント起因なら 500 推奨
            if not value:  # None または b""
                logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.REDIS_NO_DATA,
                           f"中間データ取得失敗:グループ管理情報:key=[{group_manage_key}]")
                raise Exception("グループ管理情報取得エラー")

            # 取得内容結合
            group_manage_data.extend(value)

        self.header = b''.join(
            [header_bytes, group_num, percel_num, group_manage_data])

    #  5)「グループ番号」に レベル対応の固定値(Lv=-1:3,他：2)を設定
    #  6)「背景デフォルトフラグ」に、データ種別=0の有無での固定値
    #       （有：0(陸)，無：1(海)）を設定
    #        注：分割情報取得時は背景デフォルトフラグは1固定
    def make_percel_head_devdata(self, params: dict):

        if "-1" == params["levelNo"]:
            self.percel_head += b''.join([
                self.percel_head,
                (0x00000024).to_bytes(4, byteorder="big"),  # パーセル情報のサイズ:4
                (0x0003).to_bytes(2, byteorder="big"),      # グループ番号のサイズ:2
                (0x01).to_bytes(1, byteorder="big"),        # 背景デフォルトフラグのサイズ:1
                (0x00).to_bytes(1, byteorder="big")         # Reservedのサイズ:1
            ])
        else:
            self.percel_head += b''.join([
                self.percel_head,
                (0x00000024).to_bytes(4, byteorder="big"),  # パーセル情報のサイズ:4
                (0x0002).to_bytes(2, byteorder="big"),      # グループ番号のサイズ:2
                (0x01).to_bytes(1, byteorder="big"),        # 背景デフォルトフラグのサイズ:1
                (0x00).to_bytes(1, byteorder="big")         # Reservedのサイズ:1
            ])

    # 分割情報取得用のパーセル情報ヘッダ生成
    def make_percel_head(self, intermediate_value: dict):

        # 高速化
        # パーセル情報[0]
        percel_head_parts = []

        # パーセル情報のサイズ:4
        percel_head_parts.append(self.percel_size.to_bytes(4, byteorder="big"))

        #  5)「グループ番号」に レベル対応の固定値(Lv=-1:3,他：2)を設定
        # グループ番号:2（レベルNoに応じて値を変える）
        if "-1" == self.params["levelNo"]:
            percel_head_parts.append((0x0003).to_bytes(2, byteorder="big"))
        else:
            percel_head_parts.append((0x0002).to_bytes(2, byteorder="big"))

        #  6)「背景デフォルトフラグ」に、データ種別=0の有無での固定値
        #   （有：0(陸)，無：1(海)）を設定
        #       注：分割情報取得以外の時は0固定
        percel_head_parts.append((0x00).to_bytes(
            1, byteorder="big"))        # 背景デフォルトフラグ:1

        # Reserved:1
        percel_head_parts.append((0x00).to_bytes(1, byteorder="big"))

        # 結合して代入（+= を使う場合）
        self.percel_head += b''.join(percel_head_parts)

    # # 9.緯度経度→識別FLG-1/8秒変換
    def setDirectOctsec(self, latlon_value: bytes):
        self.divNum = latlon_value[0:4]     # 分割情報
        self.lat = latlon_value[4:8]     # パーセル左下基準経度
        self.lon = latlon_value[8:12]    # パーセル左下基準緯度

    # 8.パーセル分割情報変換
    # MemmoryDBバリュー（パーセル分割情報）値から、分割数(div_count), 左下経度(lb_long), 左下緯度(lb_lat)を取得する。
    # 成功 True
    # 失敗 False
    def splitDivInfo(self):

        #  7)「パーセルID」に、CGI引数(pid)の分解値を設定
        # パーセルID
        self.percel_id += b''.join([
            get_byte_hex(self.params["levelNo"], 2),    # レベル番号
            get_byte_hex(self.params["blockSetNo"], 2),  # ブロックセット番号
            get_byte_hex(self.params["blockNo"], 2),    # ブロック番号
            get_byte_hex(self.params["parcelNo"], 2)    # パーセル番号
        ])

        # 中間データ－テーブル名：パーセル分割情報（tbl_parcel_div_data）
        # 開始バイト    バイト数    意味    備考
        #                           識別情報                            初期は未設定
        # 1             2           レベル番号(level)                   6,5,4,3,2,1,0,-1 (左0埋め2桁)
        # 3             4           ブロックセット番号(blockset_no)     0 ～ 63 (左0埋め4桁：8x8)
        # 7             4           ブロック番号(block_no)              0 ～ 1023 (左0埋め4桁：32x32)
        # 11            4           パーセル番号(parcel_no)             0 ～ 1023 (左0埋め4桁：32x32)
        #                           桁合わせ                            "0"文字埋め。初期は未設定
        # 15            2           世代番号                            0 ～ 99 (0パディング2桁)
        #  8)「経度方向分割数」「緯度方向分割数」に、(3)3)の分解数（平方根）を設定
        # 3) レベル(Lv)，ブロックセット(Bs)，ブロック(Bl)，パーセル(Pc)，世代番号でMemoryDBのパーセル分割数、左下経度、左下緯度を取得(get)する。

        # "pid":"0_36_27_536_1"
        # 00 0036 0027 0536 70
        latlon_key = (
            self.params["levelNo"].zfill(2)
            + self.params["blockSetNo"].zfill(4)
            + self.params["blockNo"].zfill(4)
            + self.params["parcelNo"].zfill(4)
            + self.params["generation"].zfill(2)
        )

        # 8.パーセル分割情報取得
        try:
            latlon_value = self.redis_client.get(latlon_key)

            # raise Exception("その他")
            # latlon_value = None

        except Exception as e:
            logOut.log(LogLebel.ERROR, 500, LogCodes.REDIS_UNKNOWN,
                       f"latlon_key[{latlon_key}] 中間データ取得失敗:パーセル分割情報: {e}")
            raise Exception("その他")

        # データ取得できたか？
        if None == latlon_value:
            # データ取得できなかったのでここで終了
            return False

        self.divNum = latlon_value[0:4]     # 分割情報
        self.lat = latlon_value[4:8]     # パーセル左下基準経度
        self.lon = latlon_value[8:12]    # パーセル左下基準緯度

        # 平方根を計算
        idivNum = int.from_bytes(self.divNum, 'big')
        sqrt_value = int(math.sqrt(idivNum))

        #   経度方向分割数:2
        #   緯度方向分割数:2
        dev_data = get_byte_hex(str(sqrt_value), 2)
        self.percel_id += b''.join([
            dev_data,  # 経度方向分割数
            dev_data   # 緯度方向分割数
        ])

        # ・(3)3)の分割数≠1かつ分割番号指定=-1の場合、10)～11)をスキップする。
        if ("-1" == self.params["divNo"]) and (1 != idivNum):
            idivNum = int.from_bytes(self.divNum, 'big')
        else:
            idivNum = int(self.params["divNo"])

        #   分割番号:4
        self.percel_id += get_byte_hex(str(idivNum), 4)
        self.divNum = idivNum        # 分割数

        #  9)「左下基準経度」「左下基準緯度」に、計算値を設定
        self.percel_id += b''.join([
            self.lat,  # 西東経識別フラグ[1bit]：パーセル左下基準経度(1/8秒):4
            self.lon   # 南北緯識別フラグ[1bit]：パーセル左下基準緯度(1/8秒):4
        ])

        return True

    # 10)(3)2)抽出データを元に「パーセル単位データへのオフセット」
    def make_percel_data(self):

        # パーセル情報サイズ取得:36
        percel_id_size = 32         # 固定値32
        percel_mng_head_size = 4    # 固定値4

        # パーセル単位データ管理数(b0)
        # Reserved
        # 中間データの個数
        # 設定するデータ数を算出している
        # 12) 出力データから「パーセル単位データ管理数(b0)」「パーセル情報のサイズ」
        #   を設定する。（10)～11)処理がスキップされた場合、0(固定値)を設定する。）
        #       パーセル単位データ管理数(b0)
        #           ＝  (3)2)データ種別(0～)の数(Phase1:6(固定値))
        #               ※(3)3)の分割数≠1かつ分割番号指定=-1の場合、0(固定値)を設定する。
        self.percel_data_num = len(self.intermediate_value)
        self.percel_id += b''.join([
            get_byte_hex(str(self.percel_data_num), 2)
        ])

        # Reserved
        Reserved2byte = (0x0000).to_bytes(2, byteorder="big")
        self.percel_data += b''.join([
            Reserved2byte
        ])

        # 「パーセル単位データのサイズ」を設定
        #   パーセル単位データへのオフセット
        #       ＝  36(パーセル情報のサイズ～パーセル単位データ管理数(b0)+Reserved)
        #       ＋  パーセル単位データ管理レコード長(12byte)
        #           ×   パーセル単位データ管理数(b0)
        #       ＋  直前までの(3)2)データ種別(0～4)抽出データサイズ
        #           ※直前の(3)2)データ種別(0～4)抽出データが存在しない場合、サイズを4とする

        # オフセット位置
        #   パーセル単位データへのオフセット
        #       ＝  36(パーセル情報のサイズ～パーセル単位データ管理数(b0)+Reserved)
        #       ＋  パーセル単位データ管理レコード長(12byte) × パーセル単位データ管理数(b0)
        offset_position = (percel_id_size + percel_mng_head_size) + \
            (12 * self.percel_data_num)

        # パーセル単位データ管理ヘッダ作成
        percel_data_body = b""  # パーセルデータ格納場所
        # 中間データ（タプルのリスト）を順に処理
        for item in self.intermediate_value:
            # 各アイテムが (key, value) の形式のタプルであることを確認
            if isinstance(item, tuple) and len(item) == 2:
                key, value = item  # タプルを key, value に展開
                value_len = 0  # value の長さ（バイト数）を初期化

                # パーセル単位データのサイズ
                #   ＝  (3)2)データ種別(0～4)抽出データサイズ
                #       ※(3)2)データ種別(0～4)抽出データが存在しない場合、サイズに4を設定する
                # 注：データが取得できていないときは、valueに[0x00000000]がセットされている

                # value が None の場合、長さは 0
                if value is None:
                    value_len = 0
                # value が bytes 型の場合、そのままの長さを取得
                elif isinstance(value, bytes):
                    value_len = len(value)
                # value が str 型の場合、UTF-8 にエンコードしてバイト長を取得
                elif isinstance(value, str):
                    value_len = len(value.encode('utf-8'))

                # データのセット
                # 11)(3)2)抽出データを「パーセル単位主要地図データ」～「パーセル単位行政界面データ」として出力データ末尾に結合
                #   ※(3)2)データ種別(0～4)抽出データが存在しない場合、4バイトの0x00000000を末尾に結合する
                # 注：データが取得できていないときは、valueに[0x00000000]がセットされている
                if 0 < value_len:
                    self.percel_data += b''.join([
                        # パーセル単位データ種別:2
                        int(key).to_bytes(2, byteorder="big"),
                        Reserved2byte,                                  # Reserved 2バイト
                        offset_position.to_bytes(
                            4, byteorder="big"),   # パーセル単位データへのオフセット:4
                        # パーセル単位データのサイズ:4
                        value_len.to_bytes(4, byteorder="big")
                    ])
                    # オフセット変更
                    offset_position += value_len
                    percel_data_body += b''.join([
                        value
                    ])

        # 管理情報とパーセルデータを結合
        self.percel_data += b''.join([
            percel_data_body
        ])

        # 12) 出力データから「パーセル単位データ管理数(b0)」「パーセル情報のサイズ」
        #   を設定する。（10)～11)処理がスキップされた場合、0(固定値)を設定する。）
        #       パーセル情報のサイズ
        #           ＝  32 + 4 ＋ パーセル単位データ管理レコード長(12byte) × パーセル単位データ管理数(b0)
        #           ＋  SUM（ (3)2)データ種別(0～)毎の抽出データサイズ ）
        # パーセルサイズを設定
        self.percel_size = offset_position

# 出力データをHTTPレスポンス形式で返すビルダークラス
# CGI毎に処理内容が頃なるため個別クラスとする


class HttpResponseBuilder:
    def __init__(self, body: Union[str, bytes, ErrorContext]):
        self.body = body

    def build_text_response(self) -> dict:
        try:
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "text/plain; charset=utf-8"},
                "body": self.body if isinstance(self.body, str) else self.body.decode('utf-8'),
                "isBase64Encoded": False
            }
        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                       f"HttpResponseBuilder: {e}")
            raise Exception("応答失敗")

    def build_json_response(self) -> dict:
        try:
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "application/json; charset=utf-8"},
                "body": json.dumps(self.body, ensure_ascii=False),
                "isBase64Encoded": False
            }
        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                       f"HttpResponseBuilder: {e}")
            raise Exception("応答失敗")

    def build_binary_response(self) -> dict:
        try:
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "application/octet-stream"},
                "isBase64Encoded": True,
                "body": base64.b64encode(self.body).decode("ascii")
            }

        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                       f"HttpResponseBuilder: {e}")
            raise Exception("応答失敗")

    def error_response(self):
        try:
            return {
                "statusCode": self.body.code,
                "headers": {
                    "Content-Type": "text/plain; charset=utf-8"
                },
                "body": f"HTTP/1.1 {self.body.code} ERROR\r\n\r\n{self.body.message}",
                "isBase64Encoded": False
            }
        except Exception as e:
            logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                       f"HttpResponseBuilder: {e}")
            # raise Exception("応答失敗")
            return {
                "statusCode": 500,
                "headers": {
                    "Content-Type": "text/plain; charset=utf-8"
                },
                "body": f"HTTP/1.1 500 ERROR\r\n\r\n 内部エラー発生しました ",
                "isBase64Encoded": False
            }


def print_hex(data):
    if isinstance(data, (bytes, bytearray)):
        print("HEX:", ' '.join(f'{b:02X}' for b in data))
    else:
        print("Error: input is not bytes or bytearray, got", type(data))

# 引数の確認


def checkArgs(event):
    params = getArgs.validate(event)

    # pidのチェック
    query = event.get('queryStringParameters', {}) or {}
    pid = query.get("pid", None)
    if None == pid or 0 == len(pid):
        logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA,
                   f"getArgs: pid[{pid}]")
        raise ValueError("pidデータエラー")
    splitPid_param = splitPid.validate(pid)
    params.update(splitPid_param)

    # Phase3追加部分
    # ---------------------
    # kind取得、チェック
    kind = query.get("kind", None)
    params["kind"] = kind

    # kind設定なし、もしくはkind=6
    # True:何もしない
    # False:kindデータエラー、ValueError
    if not (kind is None or kind == "6" or kind == "7" or kind == "8"):
        logOut.log(LOG_LEVEL_ERROR, 400, LogCodes.BASE_NO_DATA,
                   f"getArgs: kind[{kind}]")
        raise ValueError("kindデータエラー")

    return params

# フォールバック用固定60-72バイトデータ生成関数
# metadatabuilder処理時にResponseMetadataBuilderクラスのプロセスから
# 定数値を抽出して構築したバイナリデータ（60-72bytes）を返す


def Fallbackdata(redis_client, params: dict, fmtver: str) -> bytes:
    ver = convFmtver(fmtver)

    # (1) ヘッダ部: version(4bytes) + status(4bytes)
    header_bytes = struct.pack('>II', ver.version, 0)

    # (2) グループ管理情報数[0002] - 固定値2
    group_num = (0x0002).to_bytes(2, byteorder="big")

    # (3) パーセル情報数[0001] - 固定値1
    percel_num = (0x0001).to_bytes(2, byteorder="big")

    # グループ管理情報取得処理
    sub_groups = (("2", "0002"), ("3", "0003"))
    group_manage_data = bytearray()

    for label, sub_kind in sub_groups:
        group_manage_key = f"0000{sub_kind}{params['generation']}"
        try:
            value = redis_client.get(group_manage_key)
            # Redis取得失敗時またはデータなし時はゼロ埋め
            if value is None or not value:
                value = (0x0000000000000000000000000000).to_bytes(
                    12, byteorder="big")
        except Exception as e:
            # Redis接続失敗時はゼロ埋めで継続（フォールバック機能のため例外を発生させない）
            logOut.log(LogLebel.ERROR, 500, LogCodes.REDIS_NO_DATA,
                       f"Fallbackdata: Redis取得失敗（継続）:key=[{group_manage_key}]: {e}")
            value = (0x0000000000000000000000000000).to_bytes(
                12, byteorder="big")

        group_manage_data.extend(value)

    # ヘッダ結合
    header = b"".join([header_bytes, group_num, percel_num, group_manage_data])

    # (4) パーセル情報ヘッダの構成要素
    # パーセル情報のサイズ:4 - 固定値36(0x00000024)
    percel_size = (0x00000024).to_bytes(4, byteorder="big")

    # (5) グループ番号:2 - レベルに応じた固定値
    if "-1" == params.get("levelNo"):
        group_num_head = (0x0003).to_bytes(2, byteorder="big")  # Lv=-1時は3
    else:
        group_num_head = (0x0002).to_bytes(2, byteorder="big")  # その他は2

    # (6) 背景デフォルトフラグ:1 - フォールバック時は0固定
    bg_default_flag = (0x01).to_bytes(1, byteorder="big")

    # (7) Reserved:1 - 0固定
    reserved_1byte = (0x00).to_bytes(1, byteorder="big")

    # パーセル情報ヘッダ結合（8bytes）
    percel_head = b"".join(
        [percel_size, group_num_head, bg_default_flag, reserved_1byte])

    # (8) パーセルID - フォールバック用定数値で構成
    percel_id = b"".join([
        get_byte_hex(params.get("levelNo", "0"), 2),        # レベル番号(2bytes)
        get_byte_hex(params.get("blockSetNo", "0"), 2),     # ブロックセット番号(2bytes)
        get_byte_hex(params.get("blockNo", "0"), 2),        # ブロック番号(2bytes)
        get_byte_hex(params.get("parcelNo", "0"), 2),       # パーセル番号(2bytes)
        # 経度方向分割数(2bytes) - 固定値1
        (0x0001).to_bytes(2, byteorder="big"),
        # 緯度方向分割数(2bytes) - 固定値1
        (0x0001).to_bytes(2, byteorder="big"),
        # 分割番号(4bytes) - 固定値FFFFFFFF
        (0xFFFFFFFF).to_bytes(4, byteorder="big"),
        (0x0000000000000000).to_bytes(
            8, byteorder="big"),  # 左下基準経度/緯度(8bytes) - ゼロ固定
        # パーセル単位データ管理数(2byte) - 固定値0
        (0x0000).to_bytes(2, byteorder="big"),
        # Reserved(1byte) - 固定値0
        (0x0000).to_bytes(2, byteorder="big")
    ])

    # 全データ結合
    final_data = b"".join([header, percel_head, percel_id])

    return final_data


def lambda_handler(event, context):
    # (6)ログ出力
    # ログに引数などの情報を設定
    logOut.set_event(event)

    try:
        # (1)入力パラメータ解析
        # params = getArgs.validate(event)
        params = checkArgs(event)
        cid = params["cid"]
        sid = params["sid"]
        aid = params["aid"]
        kind = params["kind"]   # Phase2追加部分
        fmtver = params["fmtver"]
        data_code = params["data_code"]

        # redisへの接続
        redis_client = MemoryDBSession().get_redis_client()

        # 成功の判定
        if not redis_client:
            # 「Redis接続に失敗しました」を返す
            return HttpResponseBuilder(ErrorCodes.REDIS_CONNECTION_FAILED).error_response()

        # (2) データ世代取得
        generation = str(getGenNumber.resolve(
            redis_client, data_code)).zfill(2)

        # データ世代番号からキーを作成
        generation_str = generation
        auth_key = "ID" + generation_str
        params.update({"generation": generation_str})

        # キーを使って認証情報を検索
        RetAuth = idAuthentication.verify(
            redis_client, auth_key, cid, sid, aid)
        if 0 != RetAuth:
            # 「認証失敗」を返す
            return HttpResponseBuilder(ErrorCodes.AUTH_FAILURE).error_response()

        final_data = b""  # bytes型で初期化

        # 出力パラメータ生成クラス初期化
        # 7)「パーセルID」に、CGI引数(pid)の分解値を設定
        # 8)「経度方向分割数」「緯度方向分割数」に、(3)3)の分割数（平方根）を設定
        # 9)「左下基準経度」「左下基準緯度」に、(3)3)の左下経度・緯度を設定
        try:
            builder = ResponseMetadataBuilder(redis_client, params, fmtver)
        except ResponseMetadataBuilderError as e:
            # 中間データが見つからない場合の例外: 固定バイナリのフォールバックデータを返却
            try:
                fallback = Fallbackdata(redis_client, params, fmtver)
                return HttpResponseBuilder(fallback).build_binary_response()
            except Exception as fallback_error:
                # フォールバックも失敗した場合はエラーレスポンス
                logOut.log(LOG_LEVEL_ERROR, 500, LogCodes.BASE_EXCEPTION,
                           f"Fallbackdata失敗（中間データなし）: {fallback_error}")
                return HttpResponseBuilder(ErrorCodes.INTERMEDIATE_DATA_NOT_FOUND).error_response()

        idivNum = builder.get_divNum()

        # 分割判定
        # ------------------------------------------------------------------
        # ・(3)3)の分割数≠1かつ分割番号指定=-1の場合、10)～11)をスキップする。
        if ("-1" == params["divNo"]) and (1 < idivNum):
            # 1)ヘッダ部にフォーマット・バージョン設定
            # 2)「グループ管理情報数」に2（固定値）を設定
            # 3)「パーセル情報」に、1（固定値）を設定
            # 4)「グループ管理情報」に、(3)1)抽出データを設定
            builder.make_header()       # ヘッダ情報（bytesを返す）

            #  5)「グループ番号」に レベル対応の固定値(Lv=-1:3,他：2)を設定
            builder.make_percel_head_devdata(
                params)        # 分割情報取得用のパーセル情報ヘッダ生成

            final_data += b''.join([
                builder.get_header(),                       # ヘッダ情報
                builder.get_percel_head(),                  # パーセル情報のヘッダ
                builder.get_percel_id(),                    # パーセルID
                (0x0000).to_bytes(2, byteorder="big"),      # パーセルデータ管理数
                (0x0000).to_bytes(2, byteorder="big")       # Reserved
            ])

            # バイナリ応答を返す場合
            return HttpResponseBuilder(final_data).build_binary_response()

        # (3) 中間データ抽出
        intermediate_value = DataFetcherBuilder(redis_client, params)

        for item in intermediate_value:
            if isinstance(item, tuple) and len(item) == 2:
                key, value = item  # タプルを key, value に展開

        # データの作成を行う
        builder.set_intermediate_value(intermediate_value)

        # 1)ヘッダ部にフォーマット・バージョン設定
        # 2)「グループ管理情報数」に2（固定値）を設定
        # 3)「パーセル情報」に、1（固定値）を設定
        # 4)「グループ管理情報」に、(3)1)抽出データを設定
        builder.make_header()       # ヘッダ情報（bytesを返す）

        builder.make_percel_data()  # パーセルデータ

        # 5)「グループ番号」に レベル対応の固定値(Lv=-1:3,他：2)を設定
        # 6)「背景デフォルトフラグ」に、データ種別=0の有無での固定値
        builder.make_percel_head(intermediate_value)  # パーセル情報のヘッダ（bytesを返す）

        final_data += b''.join([
            builder.get_header(),       # ヘッダ情報
            builder.get_percel_head(),  # パーセル情報のヘッダ
            builder.get_percel_id(),    # パーセルID
            builder.get_percel_data()   # パーセルデータ
        ])

        # (5)出力パラメータ返却
        ret_data = HttpResponseBuilder(final_data).build_binary_response()

        return ret_data

    except ValueError as e:
        return HttpResponseBuilder(ErrorCodes.BAD_REQUEST).error_response()

    except Exception as e:
        print(f"lambda_handler: Exception発生: {e}")
        return HttpResponseBuilder(ErrorCodes.INTERNAL_SERVER_ERROR).error_response()
