<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Valentine's Day ❤️</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #ffeef8 0%, #ffe0f0 100%);
            min-height: 100vh;
            padding: 40px 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .notebook {
            max-width: 900px;
            width: 100%;
            background: #fff;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            position: relative;
            perspective: 2000px;
        }

        /* Notebook spiral binding effect */
        .notebook::before {
            content: '';
            position: absolute;
            left: 50px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: repeating-linear-gradient(
                to bottom,
                #e91e63 0px,
                #e91e63 20px,
                transparent 20px,
                transparent 40px
            );
            z-index: 100;
        }

        /* Cover page styles */
        .cover-page {
            background: linear-gradient(135deg, #e91e63, #f06292);
            border-radius: 8px;
            padding: 60px 40px;
            text-align: center;
            color: white;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cover-content {
            width: 100%;
        }

        .cover-heart {
            font-size: 5em;
            margin-bottom: 20px;
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.15); }
            50% { transform: scale(1); }
            75% { transform: scale(1.1); }
        }

        .cover-title {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        .cover-subtitle {
            font-size: 1.5em;
            margin-bottom: 30px;
            font-style: italic;
        }

        .cover-decoration {
            font-size: 2em;
            margin: 30px 0;
        }

        .cover-decoration span {
            display: inline-block;
            margin: 0 10px;
            animation: float 3s ease-in-out infinite;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .cover-decoration span:hover {
            transform: scale(1.3);
        }

        .cover-decoration span:nth-child(2) {
            animation-delay: 0.5s;
        }

        .cover-decoration span:nth-child(3) {
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .open-btn {
            background: white;
            color: #e91e63;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            font-family: 'Georgia', serif;
        }

        .open-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
        }

        /* Page flip effect */
        .page1, .page2, .page3 {
            position: relative;
            transform-style: preserve-3d;
            transition: transform 1s ease-in-out;
            transform-origin: left center;
            backface-visibility: hidden;
        }

        .page1.flipped {
            transform: rotateY(-180deg);
        }

        .page2 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transform: rotateY(0deg);
        }

        .page2.flipped {
            transform: rotateY(-180deg);
        }

        .page3 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transform: rotateY(0deg);
        }

        /* Navigation buttons */
        .page-nav {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
            background: white;
            padding: 15px 30px;
            border-radius: 50px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        .page-nav button {
            background: linear-gradient(135deg, #e91e63, #f06292);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            font-size: 1em;
            cursor: pointer;
            box-shadow: 0 3px 10px rgba(233, 30, 99, 0.3);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            font-family: 'Georgia', serif;
            font-weight: 600;
        }

        .page-nav button:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(233, 30, 99, 0.4);
        }

        .page-nav button:active:not(:disabled) {
            transform: translateY(-1px);
        }

        .page-nav button:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        .page-indicator {
            position: fixed;
            top: 30px;
            right: 30px;
            background: linear-gradient(135deg, #fff5f7, #ffe0ea);
            padding: 12px 24px;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
            font-weight: bold;
            color: #e91e63;
            z-index: 1000;
            display: none;
            border: 2px solid rgba(233, 30, 99, 0.2);
            animation: slideIn 0.5s ease-out;
        }

        .page-indicator.show {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .notebook-header {
            background: linear-gradient(135deg, #e91e63, #f06292);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }

        .notebook-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .notebook-content {
            padding: 40px 40px 40px 70px;
            background: 
                repeating-linear-gradient(
                    transparent,
                    transparent 31px,
                    #e8f4f8 31px,
                    #e8f4f8 32px
                );
            min-height: 500px;
        }

        .page-title {
            font-size: 2em;
            color: #e91e63;
            margin-bottom: 30px;
            text-align: center;
            font-style: italic;
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .photo-frame {
            background: white;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            transform: rotate(-2deg);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            border: 1px solid #ddd;
        }

        .photo-frame:nth-child(even) {
            transform: rotate(2deg);
        }

        .photo-frame:hover {
            transform: rotate(0deg) scale(1.08);
            z-index: 10;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
        }

        .photo-frame img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            display: block;
        }

        .photo-caption {
            margin-top: 10px;
            text-align: center;
            font-style: italic;
            color: #555;
            font-size: 0.9em;
        }

        .love-note {
            background: #fff9c4;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #e91e63;
            border-radius: 5px;
            box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.1);
            font-size: 1.1em;
            line-height: 1.8;
            color: #333;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hearts {
            text-align: center;
            font-size: 2em;
            margin: 20px 0;
            color: #e91e63;
        }

        /* Placeholder for images */
        .photo-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, #fce4ec, #f8bbd0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e91e63;
            font-size: 3em;
        }

        /* Page 2 specific styles */
        .page2 .notebook-header {
            background: linear-gradient(135deg, #9c27b0, #ba68c8);
        }

        .page2 .page-title {
            color: #9c27b0;
        }

        .page2 .love-note {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }

        .page2 .photo-placeholder {
            background: linear-gradient(135deg, #e1bee7, #ce93d8);
        }

        /* Page 3 specific styles */
        .page3 .notebook-header {
            background: linear-gradient(135deg, #ff5722, #ff8a65);
        }

        .page3 .page-title {
            color: #ff5722;
        }

        .page3 .love-note {
            background: #ffe0b2;
            border-left: 4px solid #ff5722;
        }

        .page3 .photo-placeholder {
            background: linear-gradient(135deg, #ffccbc, #ffab91);
        }

        /* Reasons list styling */
        .reasons-list {
            background: white;
            padding: 25px;
            margin: 30px 0;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .reasons-list h3 {
            color: #e91e63;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .reasons-list ul {
            list-style: none;
            padding: 0;
        }

        .reasons-list li {
            padding: 12px 0;
            border-bottom: 1px dashed #e0e0e0;
            font-size: 1.05em;
            color: #555;
            transition: all 0.3s ease;
        }

        .reasons-list li:hover {
            transform: translateX(10px);
            color: #e91e63;
        }

        .reasons-list li:before {
            content: "💖 ";
            margin-right: 10px;
        }

        .reasons-list li:last-child {
            border-bottom: none;
        }

        /* Timeline styling */
        .timeline {
            margin: 30px 0;
        }

        .timeline-item {
            display: flex;
            margin-bottom: 30px;
            position: relative;
            opacity: 0;
            animation: fadeInLeft 0.6s ease-out forwards;
        }

        .timeline-item:nth-child(1) { animation-delay: 0.1s; }
        .timeline-item:nth-child(2) { animation-delay: 0.2s; }
        .timeline-item:nth-child(3) { animation-delay: 0.3s; }
        .timeline-item:nth-child(4) { animation-delay: 0.4s; }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .timeline-date {
            min-width: 120px;
            font-weight: bold;
            color: #e91e63;
            font-size: 1.1em;
        }

        .timeline-content {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            margin-left: 20px;
            transition: all 0.3s ease;
        }

        .timeline-item:hover .timeline-content {
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.15);
            transform: translateY(-3px);
        }

        @media (max-width: 768px) {
            .notebook-content {
                padding: 30px 20px 30px 40px;
            }
            
            .notebook-header h1 {
                font-size: 1.8em;
            }
            
            .photo-grid {
                grid-template-columns: 1fr;
            }

            .timeline-item {
                flex-direction: column;
            }

            .timeline-content {
                margin-left: 0;
                margin-top: 10px;
            }

            .page-nav {
                bottom: 20px;
                flex-direction: column;
                gap: 10px;
                padding: 12px 20px;
            }

            .page-nav button {
                padding: 10px 20px;
                font-size: 0.9em;
                width: 100%;
            }

            .cover-title {
                font-size: 2em;
            }

            .cover-subtitle {
                font-size: 1.2em;
            }

            .page-indicator {
                top: 15px;
                right: 15px;
                font-size: 0.9em;
                padding: 8px 15px;
            }

            .notebook-header h1 {
                font-size: 1.8em;
            }

            .page-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="page-indicator">Page <span id="currentPage">1</span> of 3</div>
    
    <div class="notebook">
        <!-- COVER PAGE -->
        <div class="cover-page" id="coverPage" >
            <div class="cover-content">
                <div class="cover-heart">❤️</div>
                <h1 class="cover-title">Happy Valentine's Day</h1>
                <p class="cover-subtitle">A Love Letter Just For You</p>
                <div class="cover-decoration">
                    <span>💕</span>
                    <span>💖</span>
                    <span>💗</span>
                </div>
                <button class="open-btn" onclick="openNotebook()">Open ❤️</button>
            </div>
        </div>
        <!-- PAGE 1: Our Beautiful Memories -->
        <div class="page1" id="page1" style="display: none;">
            <div class="notebook-header">
                <h1>💕 Happy Valentine's Day 💕</h1>
                <p>To the most amazing person in my life</p>
            </div>

            <div class="notebook-content">
                <h2 class="page-title">Our Beautiful Memories Together</h2>

                <div class="photo-grid">
                    <!-- Photo 1 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">❤️</div>
                        <!-- Replace with: <img src="path/to/your/image1.jpg" alt="Memory 1"> -->
                        <p class="photo-caption">Our first date</p>
                    </div>

                    <!-- Photo 2 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💖</div>
                        <!-- Replace with: <img src="path/to/your/image2.jpg" alt="Memory 2"> -->
                        <p class="photo-caption">That beautiful sunset</p>
                    </div>

                    <!-- Photo 3 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💝</div>
                        <!-- Replace with: <img src="path/to/your/image3.jpg" alt="Memory 3"> -->
                        <p class="photo-caption">Adventure together</p>
                    </div>

                    <!-- Photo 4 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💗</div>
                        <!-- Replace with: <img src="path/to/your/image4.jpg" alt="Memory 4"> -->
                        <p class="photo-caption">Just us being silly</p>
                    </div>
                </div>

                <div class="love-note">
                    <p>
                        Dear Vukki,<br><br>
                        Every moment with you feels like a beautiful page in the story of my life. 
                        You make every day brighter, every laugh louder, and every memory sweeter. 
                        Thank you for being you, and for choosing to share your life with me.
                        <br><br>
                        I love you more than words can express. ❤️
                        <br><br>
                        Forever yours,<br>
                        Vukku
                    </p>
                </div>
            </div>
        </div>

        <!-- PAGE 2: Reasons Why I Love You -->
        <div class="page2" id="page2" style="display: none;">
            <div class="notebook-header">
                <h1>💜 Why I Love You 💜</h1>
                <p>A thousand reasons and counting...</p>
            </div>

            <div class="notebook-content">
                <h2 class="page-title">The Things That Make You Special</h2>

                <div class="reasons-list">
                    <h3>Just Some of the Reasons...</h3>
                    <ul>
                        <li>Your smile lights up my entire world</li>
                        <li>The way you laugh at my terrible jokes</li>
                        <li>How you always know when I need a hug</li>
                        <li>Your kindness and compassion for everyone</li>
                        <li>The sparkle in your eyes when you're excited</li>
                        <li>How you make ordinary moments extraordinary</li>
                        <li>Your strength and courage inspire me daily</li>
                        <li>The way you hold my hand</li>
                        <li>Your beautiful heart and soul</li>
                        <li>Simply being yourself - perfectly imperfect</li>
                    </ul>
                </div>

                <div class="photo-grid">
                    <!-- Photo 5 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💜</div>
                        <!-- Replace with: <img src="path/to/your/image5.jpg" alt="Memory 5"> -->
                        <p class="photo-caption">Your beautiful smile</p>
                    </div>

                    <!-- Photo 6 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💕</div>
                        <!-- Replace with: <img src="path/to/your/image6.jpg" alt="Memory 6"> -->
                        <p class="photo-caption">Making memories</p>
                    </div>
                </div>

                <div class="love-note">
                    <p>
                        You are my best friend, my partner in crime, and my greatest adventure. 
                        Every day with you is a gift I treasure. Thank you for being the incredible 
                        person you are and for loving me just as I am.
                        <br><br>
                        Here's to many more beautiful moments together! 💜
                    </p>
                </div>
            </div>
        </div>

        <!-- PAGE 3: Our Journey Together -->
        <div class="page3" id="page3" style="display: none;">
            <div class="notebook-header">
                <h1>🧡 Our Journey Together 🧡</h1>
                <p>Every step with you is a blessing</p>
            </div>

            <div class="notebook-content">
                <h2 class="page-title">Milestones of Our Love Story</h2>

                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-date">First Meeting</div>
                        <div class="timeline-content">
                            The day our paths crossed and my life changed forever. I knew there was 
                            something special about you from the very first moment.
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">First Date</div>
                        <div class="timeline-content">
                            Nervous butterflies, endless conversation, and the beginning of something 
                            beautiful. I didn't want that night to end.
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">Our Adventures</div>
                        <div class="timeline-content">
                            From spontaneous road trips to quiet nights in, every moment with you 
                            has been an adventure I'll cherish forever.
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">Today & Beyond</div>
                        <div class="timeline-content">
                            Here we are, still writing our story. I can't wait to see what the next 
                            chapters hold for us. With you, the future looks brighter than ever.
                        </div>
                    </div>
                </div>

                <div class="photo-grid">
                    <!-- Photo 7 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">🧡</div>
                        <!-- Replace with: <img src="path/to/your/image7.jpg" alt="Memory 7"> -->
                        <p class="photo-caption">Our favorite place</p>
                    </div>

                    <!-- Photo 8 -->
                    <div class="photo-frame">
                        <div class="photo-placeholder">💛</div>
                        <!-- Replace with: <img src="path/to/your/image8.jpg" alt="Memory 8"> -->
                        <p class="photo-caption">Forever and always</p>
                    </div>
                </div>

                <div class="love-note">
                    <p>
                        My Dearest Vukki,<br><br>
                        As I look back on our journey together, I'm filled with gratitude for every 
                        moment we've shared. You've made me a better person, and loving you has been 
                        the greatest joy of my life.
                        <br><br>
                        Here's to our past, our present, and our beautiful future together. 
                        I love you today, tomorrow, and always. 🧡
                        <br><br>
                        All my love,<br>
                        Vukku
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="page-nav">
        <button id="prevBtn" onclick="previousPage()" disabled>← Previous</button>
        <button id="nextBtn" onclick="nextPage()">Next →</button>
    </div>

    <!-- <div class="page-nav">
        <button id="prevBtn" onclick="previousPage()" disabled>← Previous</button>
        <button id="nextBtn" onclick="nextPage()">Next →</button>
    </div> -->

    <script>
        let currentPage = 0;
        const totalPages = 3;

        function openNotebook() {
            document.getElementById('coverPage').style.display = 'none';
            currentPage = 1;
            updatePageDisplay();
            document.querySelector('.page-indicator').classList.add('show');
        }

        function updatePageDisplay() {
            // Hide all pages
            document.getElementById('page1').style.display = 'none';
            document.getElementById('page2').style.display = 'none';
            document.getElementById('page3').style.display = 'none';

            // Show current page
            if (currentPage > 0) {
                document.getElementById('page' + currentPage).style.display = 'block';
            }

            // Update page indicator
            document.getElementById('currentPage').textContent = currentPage;

            // Update button states
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
        }

        function nextPage() {
            if (currentPage < totalPages) {
                // Add flip animation to current page
                document.getElementById('page' + currentPage).classList.add('flipped');
                
                setTimeout(() => {
                    currentPage++;
                    updatePageDisplay();
                    document.getElementById('page' + currentPage).classList.remove('flipped');
                }, 500);
            }
        }

        function previousPage() {
            if (currentPage > 1) {
                // Add flip animation to current page
                document.getElementById('page' + currentPage).classList.add('flipped');
                
                setTimeout(() => {
                    currentPage--;  
                    updatePageDisplay();
                    document.getElementById('page' + currentPage).classList.remove('flipped');
                }, 500);
            }
        }
    </script>
</body>
</html>