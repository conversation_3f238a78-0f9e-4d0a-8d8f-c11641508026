<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Valentine's Day ❤️</title>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-pink: #ff6b9d;
            --secondary-pink: #ff8fab;
            --accent-purple: #c44569;
            --light-pink: #ffe0e6;
            --cream: #fff5f7;
            --text-dark: #2c2c2c;
            --text-light: #666;
            --shadow-light: rgba(255, 107, 157, 0.2);
            --shadow-medium: rgba(255, 107, 157, 0.3);
            --shadow-heavy: rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--light-pink) 0%, var(--cream) 50%, #ffeef8 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Floating hearts background animation */
        .floating-hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .heart {
            position: absolute;
            color: var(--primary-pink);
            font-size: 20px;
            opacity: 0.6;
            animation: float-up 8s infinite linear;
        }

        @keyframes float-up {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Main container - removed valentine-container wrapper */

        .notebook {
            max-width: 1000px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px var(--shadow-heavy);
            position: relative;
            overflow: hidden;
            transform: perspective(1000px);
            margin: 20px auto;
            min-height: 100vh;
            z-index: 10;
        }

        /* Page container with smooth transitions */
        .page-container {
            position: relative;
            width: 100%;
            height: 700px;
            overflow: hidden;
        }

        .page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            opacity: 0;
            border-radius: 20px;
        }

        .page.active {
            transform: translateX(0);
            opacity: 1;
        }

        .page.prev {
            transform: translateX(-100%);
            opacity: 0;
        }

        /* Cover page styles */
        .cover-page {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .cover-page::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .cover-content {
            position: relative;
            z-index: 2;
        }

        .cover-heart {
            font-size: 6em;
            margin-bottom: 20px;
            animation: heartbeat 2s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(255,255,255,0.5));
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1); }
            75% { transform: scale(1.15); }
        }

        .cover-title {
            font-family: 'Dancing Script', cursive;
            font-size: 4em;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255,255,255,0.5); }
            to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255,255,255,0.8); }
        }

        .cover-subtitle {
            font-size: 1.5em;
            margin-bottom: 40px;
            font-weight: 300;
            opacity: 0.9;
        }

        .open-btn {
            background: white;
            color: var(--primary-pink);
            border: none;
            padding: 18px 45px;
            font-size: 1.3em;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .open-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .open-btn:hover::before {
            left: 100%;
        }

        .open-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        /* Page header styles */
        .page-header {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            color: white;
            text-align: center;
            padding: 40px 20px;
            margin: -20px -20px 30px -20px;
            position: relative;
            overflow: hidden;
        }

        .page-header.purple {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .page-header.orange {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .page-header h1 {
            font-family: 'Dancing Script', cursive;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }

        /* Page content */
        .page-content {
            padding: 20px 40px 40px;
            height: calc(100% - 140px);
            overflow-y: auto;
        }

        /* Memory cards */
        .memories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .memory-card {
            background: white;
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            box-shadow: 0 5px 15px var(--shadow-light);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .memory-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.1), transparent);
            transition: left 0.6s;
        }

        .memory-card:hover::before {
            left: 100%;
        }

        .memory-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px var(--shadow-medium);
            border-color: var(--primary-pink);
        }

        .memory-image {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            border-radius: 10px;
            background: linear-gradient(135deg, var(--light-pink), var(--cream));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1em;
            box-shadow: 0 3px 10px var(--shadow-light);
            position: relative;
            overflow: hidden;
            border: 2px solid white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .memory-image:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px var(--shadow-medium);
        }

        .photo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            transition: all 0.3s ease;
        }

        .photo-placeholder:hover {
            background: linear-gradient(45deg, #e9ecef, #dee2e6);
            border-color: var(--primary-pink);
            color: var(--primary-pink);
        }

        .photo-placeholder .upload-icon {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .photo-placeholder .upload-text {
            font-size: 0.8em;
            font-weight: 500;
        }

        .memory-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            border-radius: 12px;
            display: block;
            background: white;
        }

        .memory-image.has-image .photo-placeholder {
            display: none;
        }

        .memory-image.has-image {
            background: none;
            border: 3px solid var(--primary-pink);
        }

        .memory-image.has-image::after {
            content: 'Click to change';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            text-align: center;
            padding: 5px;
            font-size: 0.7em;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 0 0 12px 12px;
        }

        .memory-image.has-image:hover::after {
            opacity: 1;
        }

        .image-upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .memory-card h3 {
            color: var(--text-dark);
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1em;
        }

        .memory-card p {
            color: var(--text-light);
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* Love letter styles */
        .love-letter {
            background: linear-gradient(135deg, var(--light-pink), white);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid var(--primary-pink);
            box-shadow: 0 10px 30px var(--shadow-light);
            position: relative;
        }

        .love-letter.purple {
            background: linear-gradient(135deg, #f4e6ff, white);
            border-left-color: #9b59b6;
        }

        .love-letter.orange {
            background: linear-gradient(135deg, #fff3e0, white);
            border-left-color: #f39c12;
        }

        .love-letter h3 {
            font-family: 'Dancing Script', cursive;
            font-size: 1.8em;
            color: var(--accent-purple);
            margin-bottom: 15px;
        }

        .love-letter p {
            color: var(--text-dark);
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 1em;
        }

        .signature {
            font-family: 'Dancing Script', cursive;
            font-size: 1.3em;
            font-weight: 600;
            color: var(--primary-pink);
            text-align: right;
            margin-top: 20px !important;
        }

        /* Reasons grid */
        .reasons-container h3 {
            text-align: center;
            color: var(--text-dark);
            margin-bottom: 30px;
            font-size: 1.5em;
            font-weight: 600;
        }

        .reasons-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 40px;
        }

        .reason-item {
            display: flex;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 15px var(--shadow-light);
            transition: all 0.3s ease;
            border-left: 4px solid var(--primary-pink);
        }

        .reason-item:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .reason-icon {
            font-size: 1.5em;
            margin-right: 15px;
            min-width: 40px;
        }

        .reason-item p {
            color: var(--text-dark);
            font-weight: 500;
            line-height: 1.4;
        }

        /* Timeline styles */
        .timeline {
            position: relative;
            margin: 40px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, var(--primary-pink), var(--secondary-pink));
            border-radius: 2px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 40px;
            padding-left: 80px;
        }

        .timeline-marker {
            position: absolute;
            left: 18px;
            top: 10px;
            width: 24px;
            height: 24px;
            background: var(--primary-pink);
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 0 0 3px var(--primary-pink);
            z-index: 2;
        }

        .timeline-content {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px var(--shadow-light);
            border-left: 4px solid var(--primary-pink);
            transition: all 0.3s ease;
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px var(--shadow-medium);
        }

        .timeline-content h4 {
            color: var(--accent-purple);
            font-size: 1.3em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .timeline-content p {
            color: var(--text-dark);
            line-height: 1.6;
            font-size: 0.95em;
        }

        /* Navigation styles */
        .navigation {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: none;
            align-items: center;
            gap: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 50px;
            box-shadow: 0 10px 30px var(--shadow-light);
            backdrop-filter: blur(10px);
            transition: opacity 0.3s ease;
        }

        .navigation.show {
            display: flex;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            color: white;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px var(--shadow-light);
        }

        .nav-btn:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            opacity: 0.5;
        }

        .page-indicator {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 1.1em;
            min-width: 60px;
            text-align: center;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .notebook {
                border-radius: 15px;
            }

            .page-container {
                height: 600px;
            }

            .cover-title {
                font-size: 3em;
            }

            .cover-subtitle {
                font-size: 1.2em;
            }

            .page-header h1 {
                font-size: 2em;
            }

            .page-content {
                padding: 15px 20px 30px;
            }

            .memories-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 15px;
            }

            .memory-image {
                width: 80px;
                height: 80px;
                font-size: 1.2em;
            }

            .timeline::before {
                left: 20px;
            }

            .timeline-item {
                padding-left: 60px;
            }

            .timeline-marker {
                left: 8px;
            }

            .navigation {
                gap: 20px;
                padding: 12px 25px;
            }

            .nav-btn {
                width: 45px;
                height: 45px;
            }
        }

        /* AOS Animation styles */
        [data-aos] {
            opacity: 0;
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        [data-aos].aos-animate {
            opacity: 1;
        }

        [data-aos="fade-up"] {
            transform: translateY(30px);
        }

        [data-aos="fade-up"].aos-animate {
            transform: translateY(0);
        }

        [data-aos="slide-right"] {
            transform: translateX(-30px);
        }

        [data-aos="slide-right"].aos-animate {
            transform: translateX(0);
        }

        [data-aos="fade-left"] {
            transform: translateX(30px);
        }

        [data-aos="fade-left"].aos-animate {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <!-- Floating hearts background -->
    <div class="floating-hearts" id="floatingHearts"></div>

    <div class="notebook">
            <div class="page-container">
                <!-- COVER PAGE -->
                <div class="page cover-page active" id="coverPage">
                    <div class="cover-content">
                        <div class="cover-heart">❤️</div>
                        <h1 class="cover-title">Happy Valentine's Day</h1>
                        <p class="cover-subtitle">A Love Letter Just For You</p>
                        <button class="open-btn" onclick="openNotebook()">
                            <span>Open My Heart ❤️</span>
                        </button>
                    </div>
                </div>

                <!-- PAGE 1: Our Beautiful Memories -->
                <div class="page" id="page1">
                    <div class="page-header">
                        <h1>💕 Our Beautiful Memories 💕</h1>
                        <p>Every moment with you is a treasure</p>
                    </div>
                    <div class="page-content">
                        <div class="memories-grid">
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="100">
                                <img src="valentine/First.DNG" alt="">
                                <h3>Our First Date</h3>
                                <p>The beginning of our beautiful story</p>
                            </div>
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="200">
                                <img src="valentine/Love.JPG" alt="">
                                <h3>That Perfect Sunset</h3>
                                <p>When the world stood still for us</p>
                            </div>
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="300">
                                <img src="valentine/Adventure.JPG" alt="">
                                <h3>Our Adventures</h3>
                                <p>Exploring the world hand in hand</p>
                            </div>
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="400">
                                <img src="valentine/fun.DNG" alt="">
                                <h3>Just Being Us</h3>
                                <p>Perfect moments of pure happiness</p>
                            </div>
                        </div>
                        <div class="love-letter" data-aos="fade-up" data-aos-delay="500">
                            <h3>My Dearest Vukki,</h3>
                            <p>Every moment with you feels like a beautiful page in the story of my life. You make every day brighter, every laugh louder, and every memory sweeter. Thank you for being you, and for choosing to share your life with me.</p>
                            <p class="signature">Forever yours, Vukku ❤️</p>
                        </div>
                    </div>
                </div>

                <!-- PAGE 2: Why I Love You -->
                <div class="page" id="page2">
                    <div class="page-header purple">
                        <h1>💜 Why I Love You 💜</h1>
                        <p>A few reasons and counting...</p>
                    </div>
                    <div class="page-content">
                        <div class="reasons-container">
                            <h3>The Things That Make You Special</h3>
                            <div class="reasons-grid">
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="100">
                                    <span class="reason-icon">✨</span>
                                    <p>Your smile lights up my entire world</p>
                                </div>
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="200">
                                    <span class="reason-icon">😄</span>
                                    <p>The way you laugh at my terrible jokes</p>
                                </div>
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="300">
                                    <span class="reason-icon">🤗</span>
                                    <p>How you always know when I need a you</p>
                                </div>
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="400">
                                    <span class="reason-icon">💝</span>
                                    <p>Your kindness and compassion for everyone</p>
                                </div>
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="500">
                                    <span class="reason-icon">⭐</span>
                                    <p>The sparkle in your eyes when you're excited</p>
                                </div>
                                <div class="reason-item" data-aos="slide-right" data-aos-delay="600">
                                    <span class="reason-icon">🌟</span>
                                    <p>How you make ordinary moments extraordinary</p>
                                </div>
                            </div>
                        </div>

                        <div class="memories-grid" style="margin-top: 40px;">
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="600">
                                <img src="valentine/Smile.JPG" alt="">
                                <h3>Your Beautiful Smile</h3>
                                <p>The light of my world</p>
                            </div>
                            <div class="memory-card" data-aos="fade-up" data-aos-delay="650">
                                <img src="valentine/memories.jpg" alt="">
                                <h3>Making Memories</h3>
                                <p>Every moment counts</p>
                            </div>
                        </div>

                        <div class="love-letter purple" data-aos="fade-up" data-aos-delay="700">
                            <p>You are my best friend, my partner in crime, and my greatest adventure. Every day with you is a gift I treasure. Thank you for being the incredible person you are.</p>
                            <p class="signature">Here's to many more beautiful moments together! 💜</p>
                        </div>
                    </div>
                </div>

                <!-- PAGE 3: Our Journey -->
                <div class="page" id="page3">
                    <div class="page-header orange">
                        <h1>🧡 Our Journey Together 🧡</h1>
                        <p>Every step with you is a blessing</p>
                    </div>
                    <div class="page-content">
                        <div class="timeline">
                            <div class="timeline-item" data-aos="fade-left" data-aos-delay="100">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>First Meeting</h4>
                                    <p>The day our paths crossed and my life changed forever. I knew there was something special about you from the very first moment.</p>
                                </div>
                            </div>
                            <div class="timeline-item" data-aos="fade-left" data-aos-delay="200">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>Our Adventures</h4>
                                    <p>From spontaneous trips to quiet nights walks, every moment with you has been an adventure I'll cherish forever.</p>
                                </div>
                            </div>
                            <div class="timeline-item" data-aos="fade-left" data-aos-delay="300">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>Today & Beyond</h4>
                                    <p>Here we are, still writing our story. I can't wait to see what the next chapters hold for us. With you, the future looks brighter than ever.</p>
                                </div>
                            </div>
                        </div>

                        <div class="love-letter orange" data-aos="fade-up" data-aos-delay="500">
                            <h3>My Dearest Vukki,</h3>
                            <p>As I look back on our journey together, I'm filled with gratitude for every moment we've shared. You've made me a better person, and loving you has been the greatest joy of my life.</p>
                            <p class="signature">All my love, Vukku 🧡</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="navigation">
                <button class="nav-btn" id="prevBtn" onclick="previousPage()" disabled>
                    <span>←</span>
                </button>
                <div class="page-indicator">
                    <span id="currentPageNum">1</span> / <span id="totalPages">3</span>
                </div>
                <button class="nav-btn" id="nextBtn" onclick="nextPage()">
                    <span>→</span>
                </button>
            </div>
    </div>

    <script>
        let currentPage = 0;
        const totalPages = 3;
        let isTransitioning = false;

        // Initialize floating hearts
        function createFloatingHearts() {
            const heartsContainer = document.getElementById('floatingHearts');
            const hearts = ['❤️', '💕', '💖', '💗', '💝', '💘'];

            setInterval(() => {
                if (Math.random() < 0.3) {
                    const heart = document.createElement('div');
                    heart.className = 'heart';
                    heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
                    heart.style.left = Math.random() * 100 + '%';
                    heart.style.animationDuration = (Math.random() * 3 + 5) + 's';
                    heart.style.fontSize = (Math.random() * 10 + 15) + 'px';
                    heartsContainer.appendChild(heart);

                    setTimeout(() => {
                        heart.remove();
                    }, 8000);
                }
            }, 500);
        }

        // Simple AOS (Animate On Scroll) implementation
        function initAOS() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('aos-animate');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('[data-aos]').forEach(el => {
                observer.observe(el);
            });
        }

        function openNotebook() {
            if (isTransitioning) return;

            isTransitioning = true;
            const coverPage = document.getElementById('coverPage');
            const page1 = document.getElementById('page1');
            const navigation = document.querySelector('.navigation');

            // Animate cover page out
            coverPage.style.transform = 'translateX(-100%) rotateY(-15deg)';
            coverPage.style.opacity = '0';

            setTimeout(() => {
                coverPage.classList.remove('active');
                page1.classList.add('active');
                currentPage = 1;

                // Show navigation
                navigation.classList.add('show');
                updateNavigation();

                // Trigger animations for page 1
                setTimeout(() => {
                    initAOS();
                    isTransitioning = false;
                }, 100);
            }, 400);
        }

        function nextPage() {
            if (currentPage >= totalPages || isTransitioning) return;

            isTransitioning = true;
            const currentPageEl = document.getElementById(`page${currentPage}`);
            const nextPageEl = document.getElementById(`page${currentPage + 1}`);

            // Animate current page out
            currentPageEl.style.transform = 'translateX(-100%) rotateY(-15deg)';
            currentPageEl.style.opacity = '0';

            setTimeout(() => {
                currentPageEl.classList.remove('active');
                currentPageEl.classList.add('prev');
                currentPageEl.style.transform = '';
                currentPageEl.style.opacity = '';

                nextPageEl.classList.add('active');
                nextPageEl.style.transform = '';
                nextPageEl.style.opacity = '';

                currentPage++;
                updateNavigation();

                // Reset animations for new page
                setTimeout(() => {
                    resetAnimations();
                    initAOS();
                    isTransitioning = false;
                }, 100);
            }, 400);
        }

        function previousPage() {
            if (currentPage <= 1 || isTransitioning) return;

            isTransitioning = true;
            const currentPageEl = document.getElementById(`page${currentPage}`);
            const prevPageEl = document.getElementById(`page${currentPage - 1}`);

            // Animate current page out
            currentPageEl.style.transform = 'translateX(100%) rotateY(15deg)';
            currentPageEl.style.opacity = '0';

            setTimeout(() => {
                currentPageEl.classList.remove('active');
                currentPageEl.style.transform = '';
                currentPageEl.style.opacity = '';

                prevPageEl.classList.remove('prev');
                prevPageEl.classList.add('active');
                prevPageEl.style.transform = '';
                prevPageEl.style.opacity = '';

                currentPage--;
                updateNavigation();

                // Reset animations for new page
                setTimeout(() => {
                    resetAnimations();
                    initAOS();
                    isTransitioning = false;
                }, 100);
            }, 400);
        }

        function updateNavigation() {
            document.getElementById('currentPageNum').textContent = currentPage;
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
        }

        function resetAnimations() {
            document.querySelectorAll('[data-aos]').forEach(el => {
                el.classList.remove('aos-animate');
            });
        }

        // Handle image upload
        function handleImageUpload(event, placeholderId) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const placeholder = document.getElementById(placeholderId);
                    const memoryImage = placeholder.parentElement;

                    // Create and insert the image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = 'Uploaded memory';
                    img.style.cssText = `
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                        border-radius: 12px;
                        position: absolute;
                        top: 0;
                        left: 0;
                        background: white;
                    `;

                    // Add the image and mark container as having image
                    memoryImage.appendChild(img);
                    memoryImage.classList.add('has-image');

                    // Add click handler to change image
                    img.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const input = memoryImage.querySelector('input[type="file"]');
                        input.click();
                    });
                };
                reader.readAsDataURL(file);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                if (currentPage === 0) {
                    openNotebook();
                } else {
                    nextPage();
                }
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                previousPage();
            }
        });

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', () => {
            createFloatingHearts();
            updateNavigation();

            // Add some initial delay for cover page animations
            setTimeout(() => {
                document.querySelector('.cover-content').style.animation = 'fadeInUp 1s ease-out';
            }, 500);
        });

        // Add fadeInUp animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
