import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { motion } from 'framer-motion';
import 'leaflet/dist/leaflet.css';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON>ayer, useMap } from 'react-leaflet';

const MapComponent = ({ lat, lon, zoom }) => {
  const map = useMap();
  useEffect(() => {
    map.setView([lat, lon], zoom);
  }, [lat, lon, zoom, map]);
  return null;
};

const SearchMapApp = () => {
  const [mapState, setMapState] = useState(() => JSON.parse(localStorage.getItem('mapState')) || { lat: 35.6895, lon: 139.6999, zoom: 12 });
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState(() => JSON.parse(localStorage.getItem('searchHistory')) || []);
  const [showSearch, setShowSearch] = useState(true);

  useEffect(() => {
    localStorage.setItem('mapState', JSON.stringify(mapState));
  }, [mapState]);

  const performSearch = async (query) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/search?q=${encodeURIComponent(query)}`);
      const data = await response.json();
      setSearchResults(data.results);
      if (!searchHistory.includes(query)) {
        const updatedHistory = [query, ...searchHistory.slice(0, 4)];
        setSearchHistory(updatedHistory);
        localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      performSearch(searchQuery);
    }
  };

  return (
    <div className="flex h-screen">
      <motion.button
        className="fixed top-4 left-4 z-50 p-2 bg-blue-600 text-white rounded-xl shadow-md"
        whileHover={{ scale: 1.1 }}
        onClick={() => setShowSearch(!showSearch)}
      >
        {showSearch ? 'Hide Search' : 'Show Search'}
      </motion.button>
      <div className="flex-grow">
        <MapContainer center={[mapState.lat, mapState.lon]} zoom={mapState.zoom} className="h-full w-full">
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
          {searchResults.map((result, idx) => (
            <Marker key={idx} position={[result.latitude, result.longitude]} />
          ))}
          <MapComponent lat={mapState.lat} lon={mapState.lon} zoom={mapState.zoom} />
        </MapContainer>
      </div>
      {showSearch && (
        <motion.div initial={{ x: 300 }} animate={{ x: 0 }} exit={{ x: 300 }} className="w-80 bg-white shadow-lg p-4 overflow-y-auto">
          <div className="mb-4">
            <Input
              placeholder="Enter search term..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              list="search-history"
            />
            <datalist id="search-history">
              {searchHistory.map((term, idx) => (
                <option key={idx} value={term} />
              ))}
            </datalist>
            <Button onClick={handleSearch} className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white">
              Search
            </Button>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">Results</h3>
            {isLoading ? (
              <p className="text-center text-gray-500">Loading...</p>
            ) : (
              searchResults.map((result, idx) => (
                <Card key={idx} className="mb-2 cursor-pointer" onClick={() => setMapState({ lat: result.latitude, lon: result.longitude, zoom: 15 })}>
                  <CardContent>
                    <p className="font-bold">{result.name}</p>
                    <p className="text-sm text-gray-500">{result.branch}</p>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default SearchMapApp;
