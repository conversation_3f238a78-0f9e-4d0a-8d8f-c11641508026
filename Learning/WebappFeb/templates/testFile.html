<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Search with Infinite Scroll</title>
<script src="{{api_url}}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<style>
  body, html {
    height: 100%;
    margin: 0;
    font-family: Arial, sans-serif;
  }

  main {
    display: flex;
    height: 100%;
  }

  #map {
    flex-grow: 1;
    transition: width 0.4s;
    /* position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%; */
  }

  #toogleBtn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1001;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }

  #toogleBtn:hover {
    background-color: #0056b3;
  }

  #search_and_result {
    display: none;
    width: 0;
    padding-top: 70px;
    transition: width 0.4s;
    background-color: #f8f9fa;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    z-index: 1000;
  }

  #search_and_result input, #search_and_result button {
    width: calc(100% - 20px);
    margin: 10px;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 5px;
  }

  #search_and_result button {
    background-color: #007bff;
    color: white;
    cursor: pointer;
  }

  #search_and_result button:hover {
    background-color: #0056b3;
  }

  #results {
    padding: 10px;
    overflow-y: auto;
    height: calc(100% - 80px);
  }

  #resultsList {
    list-style: none;
    padding: 0;
  }

  #resultsList li {
    padding: 10px;
    border-bottom: 1px solid #ced4da;
    cursor: pointer;
  }

  #resultsList li:hover {
    background-color: #e9ecef;
  }

  .loading {
    text-align: center;
    padding: 10px;
  }
</style>
</head>
<body onload="loadMap()">
<main>
    <div id="map" style="height: 100%; width: 100%;"></div>
    <button id="toogleBtn" onclick="toogleFunc()">Show Search</button>
    <section id="search_and_result">
        <input type="text" id="searchInput" placeholder="Search for a place">
        <button id="searchBtn">Search</button>
        <div id="results">
        <p id="totalResults"></p>
        <ul id="resultsList"></ul>
        <div class="loading" style="display: none;">Loading...</div>
        </div>
    </section>
</main>
<script>
    let map, marker;
    let mapState = {
      lat: 35.6895,
      lon: 139.6999,
      zoom: 12
    };
    const toogleBtn = document.getElementById('toogleBtn');
    const searchBox = document.getElementById('search_and_result');
    let showResults = false;   

    function loadMap() {
      if (typeof ZDC === 'undefined') {
        console.error("ZDC API failed to load.");
        return;
      }
      const latlon = new ZDC.LatLon(mapState.lat, mapState.lon);
      map = new ZDC.Map(document.getElementById('map'), {
        zoom: mapState.zoom,
        mapType: ZDC.MAPTYPE_HIGHRES_LV18,
        latlon: latlon
      });
      ZDC.addListener(map, ZDC.MAP_CLICK, function() {
        const latlon = map.getClickLatLon();
        map.moveLatLon(latlon);
        saveMapState();
      });
      map.addWidget(new ZDC.ScaleBar());
      map.addWidget(new ZDC.Control({ pos: { left: 10, bottom: 20 }, close: true }));

      function saveMapState() {
        const latlon = map.getLatLon();
        mapState = {
          lat: latlon.lat,
          lon: latlon.lon,
          zoom: map.getZoom()
        };
        localStorage.setItem('mapState', JSON.stringify(mapState));
      }
    }
    
    function toogleFunc() {
        showResults = !showResults;
        searchBox.style.display = showResults ? 'block' : 'none';
        searchBox.style.width = showResults ? '20%' : '0%';
        // document.getElementById('map').style.width = showResults ? '80%' : '100%';
        toogleBtn.textContent = showResults ? 'Hide Search' : 'Show Search';
        }

    $(document).ready(function() {
      let allResults = [], totalResults = 0, loading = false, offset = 0, limit = 40, currentQuery = '';
      let searchHistory = JSON.parse(localStorage.getItem('searchHistory')) || [];

      $("#searchInput").autocomplete({
        source: term => searchHistory.filter(q => q.toLowerCase().includes(term.term.toLowerCase())),
        minLength: 1
      });

      function updateSearchHistory(query) {
        if (!searchHistory.includes(query)) {
          searchHistory.unshift(query);
          if (searchHistory.length > 5) searchHistory.pop();
          localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
        }
      }

      function performSearch() {
        const start_Time = performance.now();
        const query = $('#searchInput').val().trim();
        if (!query) return;

        currentQuery = query;
        offset = 0;
        allResults = [];
        $('#resultsList').empty();
        updateSearchHistory(query);

        $.get(`/search?q=${encodeURIComponent(query)}&offset=${offset}&limit=${limit}`, data => {
          totalResults = data.total;
          $('#totalResults').text(`Found ${totalResults} results`);
          displayNewResults(data.results);
          offset += data.results.length;
          console.log('Time taken to search: ', performance.now() - start_Time);
        }).fail(() => $('#resultsList').html('<li>Error performing search</li>'));
      }

      function displayNewResults(results) {
        const displat_start = performance.now();
        results.forEach(result => {
          const li = $('<li></li>').append(
            $('<div></div>').css('font-weight', 'bold').text(`${result.name} ${result.branch || ''}`),
            result.address && result.postal ? $('<div></div>').css({color: '#666', fontSize: '0.7em'}).text(`〒 ${result.postal} ${result.address}`) : ''
          ).click(() => map.moveLatLon(new ZDC.LatLon(result.latitude, result.longitude)));
          $('#resultsList').append(li);
        });
        console.log('Time taken to display: ', performance.now() - displat_start);
      }

      $('#searchBtn').click(performSearch);
      $('#searchInput').keypress(e => { if (e.which == 13) performSearch(); });

      $('#results').scroll(function() {
        if ($(this).scrollTop() + $(this).height() >= $(this)[0].scrollHeight - 100 && !loading && offset < totalResults) {
          loading = true;
          $('.loading').show();
          $.get(`/moreResults?offset=${offset}&limit=${limit}`, moreData => {
            displayNewResults(moreData);
            offset += moreData.length;
          }).always(() => {
            loading = false;
            $('.loading').hide();
          });
        }
      });
    });
  </script>
</body>
</html> 