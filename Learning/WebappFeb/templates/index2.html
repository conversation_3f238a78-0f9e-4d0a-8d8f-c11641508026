<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Search with Infinite Scroll</title>
<script src="{{api_url}}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<script src="../static/markercluster-1.1.js"></script>
<style>
  body, html {
    height: 100%;
    margin: 0;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: #f8f9fa;
  }

  main {
    display: flex;
    height: 100%;
    position: relative;
  }

  #map {
    flex-grow: 1;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  #toggleBtn {
    z-index: 1001;
    padding: 12px 18px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
  }

  #toggleBtn:hover {
    transform: translateY(-2px);
    background-color: #357abd;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  #search_and_result {
    display: block;
    width: 0;
    max-width: 25%;
    padding-top: 60px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #ffffff;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    z-index: 1000;
  }

  #Circle {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1001;
    display: flex;
    gap: 12px;
    align-items: center;
    opacity: 0.7;
    padding: 10px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
  }

  #Circle-Radius {
    width: 60px;
    padding: 12px;
    border: 2px solid #e1e4e8;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
  }

  #Circle-Radius:focus {
    border-color: #4a90e2;
    outline: none;
  }

  #Circle-button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background-color: #dc3545;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  #Circle-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
  }

  #searchInput {
    width: calc(100% - 40px);
    margin: 15px 20px;
    padding: 12px 15px;
    border: 2px solid #e1e4e8;
    border-radius: 10px;
    font-size: 15px;
    transition: all 0.3s ease;
  }

  #searchInput:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  }
  #searchBtn {
    padding: 12px 20px;
    margin-left: 20px;
    border: none;
    border-radius: 8px;
    background-color: #4a90e2;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  #results {
    padding: 15px;
    overflow-y: auto;
    height: calc(100% - 100px);
  }
  #results::-webkit-scrollbar {
    width: 10px;
  }
  #results::-webkit-scrollbar-thumb {
    background-color: #4a90e2;
    border-radius: 10px;
  }
  #results::-webkit-scrollbar-track {
    background-color: #f8f9fa;
  }
  #resultlist {
    list-style-type: none;
    padding: 0;
  }
  #results ul{
    padding: 0;
    margin: 0;
  }

  #resultsList li {
    list-style-type: none;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
  }

  #resultsList li:hover {
    transform: translateX(5px);
    transform: translateY(-2px);
    background-color: #e3e1e1;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border-left: 4px solid #4a90e2;
  }
</style>
</head>
<body onload="loadMap()">
<main>
    <div id="map" style="height: 100%; width: 100%;"></div>
    <div id="Circle">
      <input type="number" name="Radius" id="Circle-Radius" placeholder="Kms" max="100" min="1" value="1">
      <button type="button" id="Circle-button" onclick="CircleBtnFunc()">Circle</button> 
      <button id="toggleBtn" onclick="toggleFunc()">◀</button>
    </div>
    <section id="search_and_result">
      <input type="search" id="searchInput" placeholder="Search for a place">
      <button id="searchBtn">Search</button>
      <div id="results">
      <p id="totalResults"></p>
      <ul id="resultsList"></ul>
      <div class="loading" style="display: none;">Loading...</div>
      </div>
    </section>
</main>
<script>
  let map, marker;
  let markers = [];  
  // let mapState = JSON.parse(localStorage.getItem('mapState')) || {
  //   centerlat: 35.6895,
  //   centerlon: 139.6999,
  //   zoom: 15
  // };
  let mapState = {
    centerlat: 35.6895,
    centerlon: 139.6999,
    zoom: 15
  };
  if (-90 <= mapState.centerlat && mapState.centerlat <= 90 && -180 <= mapState.centerlon && mapState.centerlon <= 180) {
    console.log('Using stored map state.');
  } else {
    console.error('Invalid stored map state.');
    mapState = {
      centerlat: 35.6895,
      centerlon: 139.6999,
      zoom: 15
    };
  }
  let toggleBtn, searchBox;
  let showResults = false;  
  let CircleBtn; 
  let markCircle;
  let CircleBtnOption;

  function loadMap() {
    if (typeof ZDC === 'undefined') {
      console.error("ZDC API failed to load.");
      alert("地図の読み込みに失敗しました。ページを更新してください。");
      return;
    }

    try {
      // Initialize map with stored position and zoom
      const latlon = new ZDC.LatLon(mapState.centerlat, mapState.centerlon);
      map = new ZDC.Map(document.getElementById('map'), {
        zoom: mapState.zoom,
        mapType: ZDC.MAPTYPE_HIGHRES_LV18,
        latlon: latlon
      });

      clearMarkers();
      updateMarkers();
      
      // Save map state whenever it changes
      function saveMapState() {
        const latlon = map.getLatLon();
        mapState = {
          centerlat: latlon.lat,
          centerlon: latlon.lon,
          zoom: map.getZoom()
        };
        localStorage.setItem('mapState', JSON.stringify(mapState));
      }

      // Map event listeners
      ZDC.addListener(map, ZDC.MAP_CLICK, function() {
        const latlon = map.getClickLatLon();
        map.moveLatLon(latlon);
        saveMapState();
        updateMarkers();
      });

      ZDC.addListener(map, ZDC.MAP_DRAG_END, function() {
        saveMapState();
        updateMarkers();
      });

      ZDC.addListener(map, ZDC.MAP_CHG_ZOOM, function() {
        saveMapState();
        updateMarkers();
      });
      map.addWidget(new ZDC.ScaleBar());
      map.addWidget(new ZDC.Control({ pos: { left: 10, bottom: 30 }, close: true }));
    } catch (e) {
      console.error('Error initializing map:', e);
      alert("地図の初期化に失敗しました。ページを更新してください。");
    }
  }
  
  function toggleFunc() {
    showResults = !showResults;
    searchBox.style.display = showResults ? 'block' : 'none';
    searchBox.style.width = showResults ? '30%' : '0%';
    map.style.width = showResults ? '70%' : '100%';
    toggleBtn.textContent = showResults ? '▶' : '◀';
  }

  $(document).ready(function() {
    toggleBtn = document.getElementById('toggleBtn');
    searchBox = document.getElementById('search_and_result');
    CircleBtn = document.getElementById('Circle-button');
    CircleBtnOption = false;
    let allResults = [], totalResults = 0, loading = false, offset = 0, limit = 100, currentQuery = '';
    let searchHistory = JSON.parse(localStorage.getItem('searchHistory')) || [];

    $("#searchInput").autocomplete({
      source: term => searchHistory.filter(q => q.toLowerCase().includes(term.term.toLowerCase())),
      minLength: 1
    });

    function updateSearchHistory(query) {
      if (!searchHistory.includes(query)) {
        searchHistory.unshift(query);
        if (searchHistory.length > 5) searchHistory.pop();
        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
      }
    }

    function performSearch() {
      const start = performance.now();
      const query = $('#searchInput').val().trim();
      if (!query) return;
      
      clearMarkers();
      currentQuery = query;
      offset = 0;
      allResults = [];
      $('#resultsList').empty();
      updateSearchHistory(query);

      const mapCenter = map.getLatLon();
      $.get(`/search?q=${encodeURIComponent(query)}&offset=${offset}&limit=${limit}&lat=${mapCenter.lat}&lon=${mapCenter.lon}`, data => {
        totalResults = data.total;
        $('#totalResults').text(`Found ${totalResults} results`);
        displayNewResults(data.results);
        updateMarkers();
        offset += data.results.length;
      }).fail(() => $('#resultsList').html('<li>Error performing search</li>'));
    }

    function displayNewResults(results) {
      results.forEach(result => {
        const li = $('<li></li>')
          .attr('data-id', result.id)
          .append(
            $('<div></div>').css('font-weight', 'bold').text(`${result.name} ${result.branch || ''}`),
            result.address && result.postal ? 
              $('<div></div>').css({color: '#666', fontSize: '0.7em'})
                .text(`〒 ${result.postal} ${result.address}`) : ''
          ).click(() => {
            map.moveLatLon(new ZDC.LatLon(result.latitude, result.longitude));
            updateMarkers();
            if (!showResults) {
              toggleFunc();
            }
          });
        $('#resultsList').append(li);
      });
}

    $('#searchBtn').click(performSearch);
    $('#searchInput').keypress(e => { if (e.which == 13) performSearch(); });

    $('#results').scroll(function() {
      if ($(this).scrollTop() + $(this).height() >= $(this)[0].scrollHeight - 100 && !loading && offset < totalResults) {
        loading = true;
        $('.loading').show();
        $.get(`/moreResults?offset=${offset}&limit=${limit}`, moreData => {
          displayNewResults(moreData);
          offset += moreData.length;
        }).always(() => {
          loading = false;
          $('.loading').hide();
        });
      }
    });
  });
  
  
  function updateMarkers() {
    const bounds = map.getLatLonBox();
    const zoom = map.getZoom();
    
    console.log('Updating markers at zoom level:', zoom);
    
    $.ajax({
        url: '/getMarkers',
        data: {
            minLat: bounds.min.lat,
            maxLat: bounds.max.lat,
            minLon: bounds.min.lon,
            maxLon: bounds.max.lon,
            zoom: zoom
        },
        success: function(data) {
            clearMarkers();
            console.log('Received clusters:', data.clusters);
            
            if (data.clusters && data.clusters.length > 0) {
                console.log('Processing clusters:', data.clusters.length);
                
                let markersToCluster = [];
                data.clusters.forEach(cluster => {
                    const position = new ZDC.LatLon(cluster.center.Latitude, cluster.center.Longitude);
                    
                    if (cluster.count === 1) {
                        const marker = new ZDC.Marker(position, {
                            color: ZDC.MARKER_COLOR_ID_RED_S
                        });
                        markers.push(marker);
                        map.addWidget(marker);
                    } else {
                        const marker = new ZDC.Marker(position, {
                            number: cluster.count,
                            color: ZDC.MARKER_COLOR_ID_RED_L
                        });
                        markersToCluster.push(marker);
                    }
                });
                
                if (markersToCluster.length > 0) {
                    const clusterOptions = {
                        map: map,
                        markers: markersToCluster,
                        maxZoom: 16,
                        radius: 100,
                        gridSize: 40,
                        method: "distance"
                    }; 
                    try {
                        const markerCluster = new ZDC.MarkerCluster(clusterOptions);
                        markerCluster.draw();
                    } catch (error) {
                        console.error('Error creating cluster:', error);
                        // Fallback: show individual markers
                        markersToCluster.forEach(marker => {
                            map.addWidget(marker);
                            markers.push(marker);
                        });
                    }
                }
            }
                    // Attach relevant data to marker for use in the event handler
                    clusterMarker.clusterData = cluster;
                    clusterMarker.position = position;
                   
                    markers.push(clusterMarker);
                    map.addWidget(clusterMarker);
                }
                console.log('Markers updated successfully.');
        },
        error: function(error) {
            console.error('Error fetching markers:', error);
        }
    });
}

function CircleBtnFunc(){
  CircleBtnOption = !CircleBtnOption;
  CircleBtn.textContent = CircleBtnOption ? 'ON' : 'OFF';
  CircleBtn.style.backgroundColor = CircleBtnOption ? '#28a745' : '#dc3545';
  if (!CircleBtnOption && markCircle) {
    map.removeWidget(markCircle);
    markCircle = null;
  }
}

function CircleRadius(location) {
  if (markCircle) {
    map.removeWidget(markCircle);
    markCircle = null;
  }
  
  const radiusInput = document.getElementById('Circle-Radius');
  const radius = parseInt(radiusInput.value);
  
  if (isNaN(radius) || radius < 1 || radius > 100) {
    alert('Please enter a radius between 1 and 100');
    radiusInput.value = '1';
    return null;
  }

  if (CircleBtnOption) {
    try {
      markCircle = new ZDC.Oval({
        latlon: location,
        x: radius * 1000,
        y: radius * 1000,
      }, {
        strokeColor: '#00FF00',
        strokeWeight: 2,
        fillColor: '#FF0000',
        lineOpacity: 0.6,
        fillOpacity: 0.2,
        circle: true
      });
      
      return markCircle;
    } catch (error) {
      console.error('Error creating circle:', error);
      return null;
    }
  }
  return null;
}

  function clearMarkers() {
    if (markers.length > 0) {
      markers.forEach(marker => {
        map.removeWidget(marker);
      });
      markers = [];
    }
  }
</script>
</body>
</html>
