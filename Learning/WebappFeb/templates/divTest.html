<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Document</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap">
<script src="{{api_url}}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="../static/style.css">
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
</head>
<body onload="loadMap()">
<main>
    <div id="Zmap" style="height: 100%; width: 100%;"></div>
    <div id="Circle">
      <input type="number" name="Radius" id="Circle-Radius" placeholder="Kms" max="100" min="1" value="1">
      <button type="button" id="Circle-button" onclick="CircleBtnFunc()">Circle</button> 
    </div>
    <button id="toggleBtn" onclick="toggleFunc()">🔍</button>
    <section id="search_and_result">
      <input type="search" id="searchInput" placeholder="入力してください">
      <button id="searchBtn">Search</button>
      <div id="results">
      <p id="totalResults"></p>
      <ul id="resultsList"></ul>
      <div class="loading" style="display: none;">Loading...</div>
      </div>
    </section>
</main>
</body>

<script>
let map, marker;
let markers = [];  
let mapState = JSON.parse(localStorage.getItem('mapState'))
if (20 <= mapState.centerlat && mapState.centerlat <= 46 && 122 <= mapState.centerlon && mapState.centerlon <= 153) {
  console.log('Using stored map state.');
} else {
  console.error('Invalid stored map state.');
  mapState = {
    centerlat: 35.6895,
    centerlon: 139.6999,
    zoom: 10
  };
}
let toggleBtn, searchBox;
let showResults = false;  
let CircleBtn; 
let markCircle;
let markCircleStatus = false;
let CircleBtnOption;
let previousZoom = mapState.zoom;

function loadMap() {
  if (typeof ZDC === 'undefined') {
    console.error("ZDC API failed to load.");
    alert("地図の読み込みに失敗しました。ページを更新してください。");
    return;
  }

  try {
    const latlon = new ZDC.LatLon(mapState.centerlat, mapState.centerlon);
    map = new ZDC.Map(document.getElementById('Zmap'), {
      zoom: mapState.zoom,
      mapType: ZDC.MAPTYPE_HIGHRES_LV18,
      latlon: latlon
    });

    clearMarkers();
    updateMarkers();
    
    function saveMapState() {
      const latlon = map.getLatLon();
      mapState = {
        centerlat: latlon.lat,
        centerlon: latlon.lon,
        zoom: map.getZoom()
      };
      localStorage.setItem('mapState', JSON.stringify(mapState));
    }

    ZDC.addListener(map, ZDC.MAP_CLICK, function() {
      const latlon = map.getClickLatLon();
      map.moveLatLon(latlon);
      saveMapState();
      updateMarkers();
    });

    ZDC.addListener(map, ZDC.MAP_DRAG_END, function() {
      saveMapState();
      updateMarkers();
    });

    ZDC.addListener(map, ZDC.MAP_CHG_ZOOM, function() {
      saveMapState();
      updateMarkers();
    });
    map.addWidget(new ZDC.ScaleBar());
    map.addWidget(new ZDC.Control({ pos: { left: 10, bottom: 30 }, close: true }));
  } catch (e) {
    console.error('Error initializing map:', e);
    alert("地図の初期化に失敗しました。ページを更新してください。");
  }
}

function toggleFunc() {
    const searchBox = document.getElementById('search_and_result');
    const mapDiv = document.getElementById('Zmap');
    const toggleBtn = document.getElementById('toggleBtn');

    searchBox.classList.toggle('open');
    toggleBtn.classList.toggle('active');

    if (searchBox.classList.contains('open')) {
        mapDiv.style.marginLeft = "20%";
        mapDiv.style.width = "80%"
    } else {
        mapDiv.style.marginLeft = "0";
        mapDiv.style.width = "100%";
    }
}
$(document).ready(function() {
  toggleBtn = document.getElementById('toggleBtn');
  searchBox = document.getElementById('search_and_result');
  CircleBtn = document.getElementById('Circle-button');
  CircleBtnOption = false;
  let allResults = [], totalResults = 0, loading = false, offset = 0, limit = 50, currentQuery = '';
  let searchHistory = JSON.parse(localStorage.getItem('searchHistory')) || [];

  $("#searchInput").autocomplete({
    source: function(request, response) {
      const term = request.term.toLowerCase();
      const matches = searchHistory.filter(q => 
        q.toLowerCase().includes(term)
      );
      response(matches);
    },
    minLength: 1,
    position: { my: "left top", at: "left bottom" },
    appendTo: "#search_and_result",
    classes: {
      "ui-autocomplete": "search-autocomplete"
    }
  });

  // Add CSS for autocomplete
  $("<style>")
    .prop("type", "text/css")
    .html(`
      .search-autocomplete {
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1002;
      }
      .search-autocomplete .ui-menu-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      .search-autocomplete .ui-menu-item:hover {
        background-color: #f5f5f5;
      }
      .search-autocomplete .ui-state-active {
        background-color: #e8f0fe;
        border: none;
        margin: 0;
      }
    `)
    .appendTo("head");

  function updateSearchHistory(query) {
    if (!searchHistory.includes(query)) {
      searchHistory.unshift(query);
      if (searchHistory.length > 10) searchHistory.pop();
      localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
    }
  }

  function performSearch() {
    const start = performance.now();
    const query = $('#searchInput').val().trim();
    if (!query) {
      alert("検索キーワードを入力してください。")
      return
    };
    
    clearMarkers();
    currentQuery = query;
    offset = 0;
    allResults = [];
    $('#resultsList').empty();
    updateSearchHistory(query);

    const mapCenter = map.getLatLon();
    $.get(`/search?q=${encodeURIComponent(query)}&offset=${offset}&limit=${limit}&lat=${mapCenter.lat}&lon=${mapCenter.lon}`, data => {
      totalResults = data.total;
      $('#totalResults').text(`結果数： ${totalResults}`);
      displayNewResults(data.results);
      updateMarkers();
      offset += data.results.length;
    }).fail(() => $('#resultsList').html('<li>Error performing search</li>'));
  }

  function displayNewResults(results) {
    results.forEach(result => {
      const li = $('<li></li>')
        .attr('data-id', result.id)
        .append(
          $('<div></div>').css('font-weight', 'bold').text(`${result.name} ${result.branch || ''}`),
          result.address && result.postal ? 
            $('<div></div>').css({color: '#666', fontSize: '0.7em'})
              .text(`〒 ${result.postal} ${result.address}`) : '',
              result.latitude && result.longitude ?
              $('<div></div>').css({color: '#666', fontSize: '0.7em'})
              .text(`${result.latitude} ${result.longitude}`) : ''
        ).click(() => {
          let LatLonPos = new ZDC.LatLon(result.latitude, result.longitude)
          map.moveLatLon(LatLonPos);
          updateMarkers();
          if (!showResults) {
            toggleFunc();
          }
        });
      $('#resultsList').append(li);
    });
}

  $('#searchBtn').click(performSearch);
  $('#searchInput').keypress(e => { if (e.which == 13) performSearch(); });

  $('#results').scroll(function() {
    if ($(this).scrollTop() + $(this).height() >= $(this)[0].scrollHeight - 50 && !loading && offset < totalResults) {
      loading = true;
      $('.loading').show();
      $.get(`/moreResults?offset=${offset}&limit=${limit}`, moreData => {
        displayNewResults(moreData);
        offset += moreData.length;
      }).always(() => {
        loading = false;
        $('.loading').hide();
      });
    }
  });
});


function updateMarkers() {
  const bounds = map.getLatLonBox();
  const zoom = map.getZoom();
  
  console.log('Updating markers at zoom level:', zoom);
  
  $.ajax({
    url: '/getMarkers',
    data: {
      minLat: bounds.min.lat,
      maxLat: bounds.max.lat,
      minLon: bounds.min.lon,
      maxLon: bounds.max.lon,
      zoom: zoom
    },
    success: function(data) {
      clearMarkers();
      console.log('Received clusters:', data.clusters);
      
      if (data.clusters && data.clusters.length > 0) {
        data.clusters.forEach(cluster => {
          const position = new ZDC.LatLon(cluster.center.Latitude, cluster.center.Longitude);
          
          if (cluster.count === 1) {
              const marker = new ZDC.Marker(position, {
                  color: ZDC.MARKER_COLOR_ID_RED_S
              });
              markers.push(marker);
              map.addWidget(marker);
              ZDC.addListener(marker, ZDC.MARKER_CLICK, function() {
                map.moveLatLon(position);
                if (CircleBtnOption) {
                  markCircle = CircleRadius(position);
                  if (markCircle) map.addWidget(markCircle);
                }
              });
              ZDC.addListener(marker, ZDC.MARKER_RIGHTCLICK, function(){
                if (markCircleStatus) {
                  if (markCircle) {
                    map.removeWidget(markCircle);
                    markCircle = null;
                  }
                }
              })
          } else if (cluster.count < 100) {
              const marker = new ZDC.Marker(position, {
                color: ZDC.MARKER_COLOR_ID_GRAY_L,
                number: cluster.count
              });
              markers.push(marker);
              map.addWidget(marker);
              ZDC.addListener(marker, ZDC.MARKER_CLICK, function() {
                const currentZoom = map.getZoom();
                const newZoom = Math.min(currentZoom + 2, 21);
                map.moveLatLon(position);
                if (currentZoom !== newZoom) {
                  map.setZoom(newZoom);
                }
              });
          } else if (cluster.count < 1000) {
              const marker = new ZDC.Marker(position, {
                color: ZDC.MARKER_COLOR_ID_GREEN_L,
                number: cluster.count
              });
              markers.push(marker);
              map.addWidget(marker);
              ZDC.addListener(marker, ZDC.MARKER_CLICK, function() {
                const currentZoom = map.getZoom();
                const newZoom = Math.min(currentZoom + 2, 21);
                map.moveLatLon(position);
                if (currentZoom !== newZoom) {
                  map.setZoom(newZoom);
                }
              });
          }
          else {
              const marker = new ZDC.Marker(position, {
                color: ZDC.MARKER_COLOR_ID_INFO_BLACK_LL,
                number: cluster.count
              });
              markers.push(marker);
              map.addWidget(marker);
              ZDC.addListener(marker, ZDC.MARKER_CLICK, function() {
                const currentZoom = map.getZoom();
                const newZoom = Math.min(currentZoom + 2, 21);
                map.moveLatLon(position);
                if (currentZoom !== newZoom) {
                  map.setZoom(newZoom);
                }
              });
          }
        });
      }
    },
    error: function(error) {
      console.error('Error fetching markers:', error);
    }
  });
}

// Add map event listeners for marker updates
ZDC.addListener(map, ZDC.MAP_DRAG_END, updateMarkers);
ZDC.addListener(map, ZDC.MAP_CHG_ZOOM, updateMarkers);

function CircleBtnFunc(location) {
  CircleBtnOption = !CircleBtnOption;
  CircleBtn.textContent = CircleBtnOption ? 'ON' : 'OFF';
  CircleBtn.style.backgroundColor = CircleBtnOption ? '#28a745' : '#dc3545';
  
  if (CircleBtnOption) {
      markCircle = CircleRadius(location);
      if (markCircle) map.addWidget(markCircle);
  } else if (markCircle) {
      map.removeWidget(markCircle);
      markCircle = null;
  }
}

function CircleRadius(location) {
  markCircleStatus = !markCircleStatus
  const radiusInput = document.getElementById('Circle-Radius');
  const radius = parseInt(radiusInput.value);
  
  if (isNaN(radius) || radius < 1 || radius > 100) {
    alert('Please enter a radius between 1 and 100');
    radiusInput.value = '1';
    return null;
  }

  if (CircleBtnOption) {
    try {
      markCircle = new ZDC.Oval({
        latlon: location,
        x: radius * 1000,
        y: radius * 1000,
      }, {
        strokeColor: '#00FF00',
        strokeWeight: 2,
        fillColor: '#FF0000',
        lineOpacity: 0.6,
        fillOpacity: 0.4,
        circle: true
      });
      
      return markCircle;
    } catch (error) {
      console.error('Error creating circle:', error);
      return null;
    }
  }
  return null;
}

function clearMarkers() {
  if (markers.length > 0) {
    markers.forEach(marker => {
      map.removeWidget(marker);
      });
      markers = [];
    }
  }
</script>
</html>
