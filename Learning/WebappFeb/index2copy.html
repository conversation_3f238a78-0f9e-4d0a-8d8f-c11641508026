<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Search with Infinite Scroll</title>
  <script src="{{api_url}}"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
  <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }
    #map_and_search {
      display: flex;
      height: 100vh;
    }
    #Map {
      width: 80%;
      height: 100%;
    }
    .search-container {
      width: 20%;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 10px;
      right: 0px;
      box-sizing: border-box;
      background-color: #f0f0f0;
      display: none;
    }
    .search-box {
      margin-bottom: 10px;
    }
    #searchInput {
      width: 70%;
      padding: 5px;
      font-size: 1em;
    }
    #searchBtn {
      padding: 5px;
      font-size: 1em;
    }
    #results {
      height: 100%;
      overflow-y: auto;
    }
    #resultsList {
      list-style-type: none;
      padding: 0;
    }
    #resultsList li {
      padding: 10px;
      border-bottom: 1px solid #ddd;
      cursor: pointer;
    }
    #resultsList li:hover {
      background-color: #f9f9f9;
    }
    .loading {
      position: fixed;
      bottom: 10px;
      right: 10px;
      padding: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 5px;
    }
    #toogleBtn {
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 10px;
      font-size: 1.5em;
      background-color: #f0f0f0;
      border: none;
      cursor: pointer;
      z-index: 1000;
    }
  </style>
</head>
<body>
  <div id="map_and_search">
    <button id="toogleBtn">三</button>
    <div class="search-container">
      <div class="search-box">
        <input type="text" id="searchInput" placeholder="Enter search term...">
        <button id="searchBtn">Search</button>
      </div>
      <div class="total-results" id="totalResults">Results</div>
      <div id="results">
        <ul id="resultsList"></ul>
        <div class="loading" style="display: none;">
          <img src="https://i.imgur.com/LLF5iyg.gif" alt="Loading..." width="30">
        </div>
      </div>
    </div>
    <div id="Map" style="height: 100%; width: 100%;"></div>
  </div>

  <script>
    let map, marker;
    let mapState;
    mapState = JSON.parse(localStorage.getItem('mapState')) || {
      lat: 35.6895,
      lon: 139.6999,
      zoom: 12
    };
    currentZoom = mapState.zoom;

    function loadMap() {
      if (typeof ZDC === 'undefined') {
        console.error("ZDC API failed to load.");
        return;
      }
      try {
        console.log(`Loading map at: ${mapState.lat}, ${mapState.lon}, zoom: ${mapState.zoom}`);
        const latlon = new ZDC.LatLon(mapState.lat, mapState.lon);
        map = new ZDC.Map(document.getElementById('Map'), {
          zoom: mapState.zoom,
          mapType: ZDC.MAPTYPE_HIGHRES_LV18,
          latlon: latlon
        });
        ZDC.addListener(map, ZDC.MAP_CLICK, moveLatLon);

        function moveLatLon() {
          var latlon = map.getClickLatLon();
          map.moveLatLon(latlon);
          saveMapState();
        }
        map.addWidget(new ZDC.ScaleBar());
        map.addWidget(new ZDC.Control({ pos: { left: 10, bottom: 20 }, close: true }));
        
        function updateCenter() {
          const latlon = map.getLatLon();
          center_lat = latlon.lat;
          center_lon = latlon.lon;
        }
        updateCenter();
        
        function saveMapState() {
          try {
            const latlon = map.getLatLon();
            const zoom = map.getZoom();
            center_lat = latlon.lat;
            center_lon = latlon.lon;
            mapState = {
              lat: latlon.lat,
              lon: latlon.lon,
              zoom: zoom
            };
            localStorage.setItem('mapState', JSON.stringify(mapState));
          } catch (e) {
            console.error('Error saving map state:', e);
          }
        }
      } catch (e) {
        console.error('Error initializing map:', e);
      }
    }
    loadMap();

    const toogleBtn = document.getElementById('toogleBtn');
    const mapContainer = document.getElementById('Map');
    const searchBox = document.querySelector('.search-container');
    let showResults = true;

    toogleBtn.addEventListener('click', () => {
      showResults = !showResults;
      document.querySelector('.search-container').style.display = showResults ? 'block' : 'none';
      mapContainer.style.width = showResults ? '80%' : '100%';
      toogleBtn.textContent = showResults ? 'Hide Search' : 'Show Search';
    });

    document.getElementById('searchBtn').addEventListener('click', () => {
      const searchTerm = document.getElementById('searchInput').value;
      if (searchTerm.trim()) {
        console.log(`Searching for: ${searchTerm}`);
        // Implement search logic here
      }
    });

    let allResults = [];
    let totalResults = 0;
    let loading = false;
    let offset = 0;
    let limit = 20;
    let currentQuery = '';
    let searchHistory = [];

    try {
      const saved = localStorage.getItem('searchHistory');
      if (saved) {
        searchHistory = JSON.parse(saved);
      }
    } catch (e) {
      console.warn('Error loading search history:', e);
      localStorage.removeItem('searchHistory');
    }

    function updateSearchHistory(query) {
      try {
        query = query.slice(0, 100);

        if (!searchHistory.includes(query)) {
          searchHistory.unshift(query);
          if (searchHistory.length > 5) {
            searchHistory.pop();
          }
          
          try {
            localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
          } catch (e) {
            if (e.name === 'QuotaExceededError' || e.name === 'QUOTA_EXCEEDED_ERR') {
              localStorage.clear();
              localStorage.setItem('searchHistory', JSON.stringify([query]));
              searchHistory = [query];
            }
          }
        }
      } catch (e) {
        console.warn('Error updating search history:', e);
        searchHistory = [];
      }
    }

    $(document).ready(function() {
      $("#searchInput").autocomplete({
        source: function(request, response) {
          const term = request.term.toLowerCase();
          const matches = searchHistory.filter(item => 
            item.toLowerCase().includes(term)
          );
          response(matches);
        },
        minLength: 1
      });

      function loadMoreResults() {
        if (loading || offset >= totalResults || !currentQuery) return;
        
        loading = true;
        $('.loading').show();
        
        $.ajax({
          url: `/moreResults?offset=${offset}&limit=${limit}`,
          method: 'GET',
          success: function(moreData) {
            if (moreData && moreData.length > 0) {
              displayNewResults(moreData, offset);
              offset += moreData.length;
              allResults = allResults.concat(moreData);
            }
          },
          error: function(error) {
            console.error('Error loading more results:', error);
          },
          complete: function() {
            loading = false;
            $('.loading').hide();
          }
        });
      }

      $('#results').scroll(function() {
        if ($(this).scrollTop() + $(this).height() >= $(this)[0].scrollHeight - 100) {
          loadMoreResults();
        }
      });

      $('#searchBtn').click(function() {
        performSearch();
      });

      $('#searchInput').keypress(function(e) {
        if (e.which == 13) {
          performSearch();
        }
      });

      function performSearch() {
        const startTime = performance.now();
        const query = $('#searchInput').val().trim();
        if (!query) return;

        currentQuery = query;
        offset = 0;
        allResults = [];
        $('#resultsList').empty();
        updateSearchHistory(query);

        $.ajax({
          url: `/search?q=${encodeURIComponent(query)}&offset=${offset}&limit=${limit}`,
          method: 'GET',
          success: function(data) {
            totalResults = data.total;
            $('#totalResults').text(`Found ${totalResults} results`);

            if (data.results.length > 0) {
              displayNewResults(data.results, 0);
              allResults = data.results;
              offset = data.results.length;
              const endTime = performance.now();
              console.log(`Search took ${endTime - startTime}ms`);
            } else {
              $('#resultsList').html('<li>No results found</li>');
            }
          },
          error: function(error) {
            console.error('Search error:', error);
            $('#resultsList').html('<li>Error performing search</li>');
          }
        });
      }
    });

    function displayNewResults(results, startOffset) {
      results.forEach((result, index) => {
        const li = $('<li></li>');
        
        const nameDiv = $('<div></div>')
          .css('font-weight', 'bold')
          .text(`${result.name} ${result.branch || ''}`);
        li.append(nameDiv);
        
        if (result.address && result.postal) {
          const addressDiv = $('<div></div>')
            .css('color', '#666')
            .css('font-size', '0.7em')
            .text(`〒 ${result.postal} ${result.address}`);
          li.append(addressDiv);
        }
        
        li.click(function() {
          alert(`Selected: ${result.latitude}, ${result.longitude}`);
          map.moveLatLon(new ZDC.LatLon(result.latitude, result.longitude));
        });
        
        $('#resultsList').append(li);
      });
    }
  </script>
</body>
</html>
