<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Div Test</title>
    <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        main {
          display: flex;
          height: 100vh;
          width: 100vw;
          margin: 0;
          padding: 0;
          position: relative;
        }
        #map {
          flex: 1;
          z-index: 0;
        }
        #toogleBtn {
          position: absolute;
          background-color: #007BFF;
          color: white;
          border: none;
          padding: 10px 20px;
          cursor: pointer;
          border-radius: 5px;
          z-index: 1;
        }
        #toogleBtn:hover {
          background-color: #0056b3;
        }
        #search_and_result {
          position: absolute;
          width: 300px;
          height: calc(100% - 40px);
          top: 20px;
          left: -350px; /* Start off-screen */
          background-color: rgba(255, 255, 255, 0.9);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          border-radius: 10px;
          padding: 20px;
          z-index: 1;
          transition: left 0.3s ease; /* Smooth transition */
        }
        #searchInput {
          width: calc(100% - 40px);
          padding: 10px;
          margin-bottom: 10px;
          border: 1px solid #ccc;
          border-radius: 5px;
        }
        #searchBtn {
          width: 100%;
          padding: 10px;
          background-color: #007BFF;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
        }
        #searchBtn:hover {
          background-color: #0056b3;
        }
        #results {
          margin-top: 20px;
        }
        #totalResults {
          font-weight: bold;
          margin-bottom: 10px;
        }
        #resultsList {
          list-style: none;
          padding: 0;
        }
        #resultsList li {
          padding: 10px;
          border-bottom: 1px solid #ccc;
        }
        .loading {
          text-align: center;
          font-size: 14px;
          color: #666;
        }
    </style>
</head>
<body>
    <main>
        <div id="map" style="height: 100%; width: 100%;"></div>
        <button id="toogleBtn">Open/Close</button>
        <section id="search_and_result">
            <input type="text" id="searchInput" placeholder="Search for a place">
            <button id="searchBtn">Search</button>
            <div id="results">
            <p id="totalResults"></p>
            <ul id="resultsList"></ul>
            <div class="loading" style="display: none;">Loading...</div>
            </div>
        </section>
    </main>
    <script>
        document.getElementById('toogleBtn').addEventListener('click', function() {
            var searchBox = document.getElementById('search_and_result');
            if (searchBox.style.left === '-350px') {
                searchBox.style.left = '20px';
            } else {
                searchBox.style.left = '-350px';
            }
        });
    </script>
</body>
</html>