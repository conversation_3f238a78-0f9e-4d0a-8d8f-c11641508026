import time
import urllib.parse
import urllib
import urllib.request
import hmac
import hashlib
import base64
def getUrl():
    # 動作確認用
    http_method = 'get'
    parameter = {
        'if_clientid'           : 'JSZ177163895ed5|F0HI1', #キー
        'if_auth_type'          : 'oauth',
        'word'                  : '', #optional
        'oauth_consumer_key'    : 'JSZ177163895ed5|F0HI1', #キー
        'oauth_signature_method': 'HMAC-SHA1',
        'oauth_timestamp'       : int(time.time()),
        'oauth_nonce'           : '', #optional
        'oauth_version'         : '1.0',
        'oauth_signature'       : ''
    }
    uri = 'https://test.api.its-mo.com/v3/loader?api=zdcmap.js,search.js&enc=UTF8&force=1&key=JSZ177163895ed5|F0HI1' #使用するAPI
    secret = 'GV47rV760ElxH0RZFEYljx3L9fg' # キーに紐づく秘密鍵

    def make_signature_hmac(http_method, parameter, uri, secret):
        # http_method
        http_method = http_method.upper()
        print('\n' + '***http_method***' + '\n' + http_method)

        # params
        if 'oauth_signature' in parameter:
            del parameter['oauth_signature']
        params = urllib.parse.urlencode(sorted(parameter.items(), key=lambda x: x[0]))
        print('\n' + '***params***' + '\n' + params)

        # uri
        parts = urllib.parse.urlparse(uri)
        scheme = parts.scheme if parts.scheme else 'http'
        port = '443' if scheme == 'https' else '80'
        host = parts.netloc
        path = parts.path
        uri = scheme + '://' + host + path
        print('\n' + '***uri***' + '\n' + uri)

        # secret
        # 秘密鍵は「指定したキー ＋ &」の形式で記述すること
        secret += '&'
        print('\n' + '***secretKey***' + '\n' + secret)

        # base_string
        base_string = http_method + '&' + urllib.parse.quote(uri, '') + '&' + urllib.parse.quote(params, '')
        print('\n' + '***basestring***' + '\n' + base_string)

        # oauth_signature
        # signatureはバイト型になる
        signature = (hmac.new(secret.encode(), base_string.encode(), hashlib.sha1).digest())
        
        # base64エンコードを行う
        signature1 = base64.b64encode(signature)
        parameter['oauth_signature'] = signature1.decode()
        print('\n' + '***oauth_signature***' + '\n' + parameter['oauth_signature'])

    # 動作確認
    make_signature_hmac(http_method, parameter, uri, secret)

    url = uri + '?' + urllib.parse.urlencode(parameter)
    print('\n' + '***URL***' + '\n' + url)
    result = None
    try:
        result = urllib.request.urlopen(url).read()
        # print('\n' + result.decode())
    except ValueError:
        print('アクセスに失敗しました。')
    except IOError:
        print('認証に失敗しました。')

    return parameter

# Example usage
params_with_signature = getUrl()
print(params_with_signature)








