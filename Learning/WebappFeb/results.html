<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>result</title>
    <style>
        main {
            height: 100vh;
            width: 100%;
            background-color: #f8f8f8;
            position: relative;
        }
        #searchBox {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
            z-index: 2;
        }
        #Keyword {
            padding: 8px;
            width: 200px;
        }
        button {
            padding: 8px 16px;
            cursor: pointer;
        }
        #results {
            position: absolute;
            top: 80px;
            right: 20px;
            display: none;
            background-color: thistle;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1;
        }
        #resultHeader {
            background-color: #007BFF;
            color: white;
            padding: 12px;
            gap: 3px;
            display: flex;
            overflow-x: auto;
            max-width: 400px;
        }
        #resultsBody {
            height: 600px;
            width: 400px;
            display: none;
            background-color: rgb(202, 67, 67);
            overflow-y: auto;
        }
        #resultList {
            padding: 12px;
            overflow-y: auto;
        }
        .search-result {
            background-color: lightblue;
            padding: 8px 12px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }
        .search-result-close {
            cursor: pointer;
            padding-left: 8px;
        }
    </style>
</head>
<body>
    <main>
        <div id="searchBox">
            <input type="text" id="Keyword" placeholder="Search anything">
            <button>Search</button>
        </div>
        <div id="results">
            <div id="resultHeader"></div>
            <div id="resultsBody">
                <div id="resultList"></div>
            </div>
        </div>
    </main>
    <script>
        const searchbox = document.getElementById('Keyword');
        const searchButton = document.querySelector('button');
        const resultHeader = document.getElementById('resultHeader');
        const resultBody = document.getElementById('resultsBody');
        const resultList = document.getElementById('resultList');
        const results = document.getElementById('results');
        const limit = 3;

        function addSearchResult(value) {
            const result = document.createElement('div');
            result.className = 'search-result';
            const text = document.createElement('span');
            text.textContent = value;
            text.style.cursor = 'pointer';
            result.addEventListener('click', () => {
                searchbox.value = value;
                resultBody.style.display = resultBody.style.display === 'block' ? 'none' : 'block';
            });
            const closeBtn = document.createElement('span');
            closeBtn.className = 'search-result-close';
            closeBtn.textContent = '✖';
            closeBtn.onclick = () => {
                result.remove();
                if (resultHeader.children.length === 0) {
                    results.style.display = 'none';
                }
            };
            result.appendChild(text);
            result.appendChild(closeBtn);
            if (resultHeader.children.length >= limit) {
                const firstChild = resultHeader.firstElementChild;
                if (firstChild) {
                    firstChild.remove();
                }
            }
            resultHeader.appendChild(result);
            results.style.display = 'block';
        }

        searchButton.addEventListener('click', () => {
            if (searchbox.value.trim()) {
                resultBody.style.display = 'block';
                addSearchResult(searchbox.value.trim());
                searchbox.value = ''; 
            }
        });

        searchbox.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchButton.click();
            }
        });
    </script>
</body>
</html>