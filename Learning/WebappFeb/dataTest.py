import csv
import mysql.connector as MySQL
import unicodedata
# from pyproj import Transformer

# Transfor coordinates system from Tokyo to Jgd2011
#transformer = Transformer.from_crs("EPSG:4301", "EPSG:4612", always_xy=True)

# def mapdata(latlon) -> list:
#     def changeType(cord):
#         try:
#             sign = 1 if cord[0] == '+' else -1
#             parts = cord[1:].split(':')
#             degrees = int(parts[0])
#             minutes = int(parts[1])
#             seconds = float(parts[2])
#             decimal = degrees + (minutes / 60) + (seconds / 3600)
#             return sign * decimal
#         except:
#             return None
#     lat_decimal = changeType(latlon[1])
#     lon_decimal = changeType(latlon[0])
#     if None in [lon_decimal, lat_decimal]:
#         return None, None
#     #lon, lat = transformer.transform(lat_decimal, lon_decimal)
#     return [lat_decimal, lon_decimal]
#     # return [lat, lon]

# def dataCheck(data):
#     for i in range(len(data)):
#         if isinstance(data[i], str):
#             data[i] = unicodedata.normalize("NFKC", data[i])
#     # Handle rows with 8 fields
#     if len(data) == 8:
#         name, branch, altname, altbranch, lat_dms, lon_dms, postal, address = data
#     # Handle rows with 6 fields
#     elif len(data) == 6:
#         name, altname, lat_dms, lon_dms, postal, address = data
#         branch, altbranch = "", ""
#     lat, lon = mapdata([lat_dms, lon_dms])
#     if lat is None or lon is None:
#         print(f"Skipping row with invalid coordinates: {data}")
#         return None

#     return [name, branch, altname, altbranch, lat, lon, postal, address]

def changeType(cords) -> str:
    try:
        sign = 1 if cords[0] == '+' else -1
        parts = cords[1:].split(':')
        degrees = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        decimal = degrees + (minutes / 60) + (seconds / 3600)
        return sign * decimal
    except Exception as e:
        return None
def location(data) -> list:
    if len(data) == 8:
        name, branch, altname, altbranch, lat_dms, lon_dms, postal, address = data
    # Handle rows with 6 fields
    elif len(data) == 6:
        name, altname, lat_dms, lon_dms, postal, address = data
        branch, altbranch = "", ""
    lat, lon = changeType(lat_dms), changeType(lon_dms)
    if lat is None or lon is None:
        print(f"Skipping row with invalid coordinates: {data}")
        return None
    for i in range(len(data)):
        if isinstance(data[i], str):
            data[i] = unicodedata.normalize("NFKC", data[i])
    return [name, branch, altname, altbranch, lat, lon, postal, address]

def main(file_path):
    try:
        mydb = MySQL.connect(
            host='localhost',
            user = 'admin',
            password = 'password'
        )
        print("Connected to MySQL successfully.")
        mycursor = mydb.cursor()
        print("Creating database and table...")
        mycursor.execute("""
        CREATE DATABASE IF NOT EXISTS smalldb
        """)    
        mycursor.execute("USE smalldb")
        mycursor.execute("""
            CREATE TABLE smalltbl (
                ID INT AUTO_INCREMENT PRIMARY KEY,
                Name VARCHAR(255) NOT NULL,
                Branch VARCHAR(255) DEFAULT NULL,
                Altname VARCHAR(255) NOT NULL,
                Altbranch VARCHAR(255) DEFAULT NULL,
                Latitude DECIMAL(10,8) NOT NULL,
                Longitude DECIMAL(11,8) NOT NULL,
                Postal VARCHAR(20) DEFAULT NULL,
                Address VARCHAR(255) DEFAULT NULL,
                Location POINT NOT NULL,
                FULLTEXT INDEX ft_search (Name, Branch, Address) WITH PARSER ngram
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)
        print("smalltbl created successfully.")
        mycursor.execute("ALTER TABLE smalltbl ADD SPATIAL INDEX sp_idx_location (Location);")
        print("Spatial index created successfully.")
        # mycursor.execute("CREATE TABLE search_history (id INT AUTO_INCREMENT PRIMARY KEY, keyword VARCHAR(255) NOT NULL,    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;")
        # print("search_history created successfully.")
        with open(file_path, 'r', encoding="shift-jis", errors="ignore") as file:
            tsv_reader = csv.reader(file, delimiter='\t')
            query = """INSERT INTO smalltbl (Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address, Location) 
           VALUES (%s, %s, %s, %s, %s, %s, %s, %s, ST_GeomFromText(%s))"""
            for i, row in enumerate(tsv_reader, start=1):
                if i > 1000:
                    break
                correct_row = location(row)
                if correct_row is None:
                    print(f"Skipping row {i}: {row}")
                    continue
                Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address = correct_row
                mycursor.execute(query, (Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address, f'POINT({Latitude} {Longitude})'))
        mydb.commit()
        print("Data insertion completed successfully.")
        # mycursor.execute("""UPDATE smalltbl 
        #     SET location = ST_GeomFromText(CONCAT('POINT(', Longitude, ' ', Latitude, ')'))
        #     WHERE location IS NULL;""")
        # print("Location data updated successfully.")
    except MySQL.Error as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error in main function: {e}")
    finally:
        if mydb.is_connected():
            mycursor.close()
            mydb.close()

if __name__ == "__main__":
    file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/WebappFeb/addresspoi.tsv'
    main(file_path)