# import MeCab
# import unidic
# from pykakasi import kakasi

# tagger = MeCab.Tagger("-Ounidic")

# def kana(text: str) -> str:
#     kks = kakasi()
#     result = kks.convert(text)
#     return "".join([item["kana"] for item in result])

# def get_onyomi(text: str) -> str:
#     parsed = tagger.parse(text)
#     onyomi_list = []
#     for line in parsed.split("\n"):
#         if line == "EOS" or not line.strip():
#             continue
#         columns = line.split("\t")
#         if len(columns) > 11:
#             onyomi = columns[11]
#             onyomi_list.append(onyomi)
#     return "".join(onyomi_list)

# def mec(text: str) -> str:
#     onyomi_text = get_onyomi(text)
#     return kana(onyomi_text)

# kanji_text = "福岡店"
# katakana_text = mec(kanji_text)
# print(katakana_text)


# import pymysql

# # Increase timeout
# connection = pymysql.connect(
#     host='localhost',
#     user='admin',
#     password='password',
#     database='poidb',
#     connect_timeout=600  # Set timeout to 600 seconds
# )
# cursor = connection.cursor()
# query = "alter table poitbl add fulltext index name_branch_address(Name, Branch, Address) WITH PARSER ngram;"
# cursor.execute(query)
# connection.commit()
# print("Index created successfully!")
import time
import pymysql 

def search(keywords):
    start_time = time.time()
    keyword = keywords.strip().lower()

    natural_query = " ".join(keyword.split())  # Convert to natural language
    print(natural_query)
    boolean_query = " ".join([f'+{word}' for word in keyword.split()])  # Convert to BOOLEAN MODE with quoted words
    print(boolean_query)
    rlike_query = "|".join(keyword.split())  # Convert to RLIKE
    print(rlike_query)
    postal_query = tuple(keyword.split())  # Convert to tuple for IN clause
    print(postal_query)
    
    connection = pymysql.connect(
        host='localhost',
        user='admin',
        password='password',
        database='poidb',
        connect_timeout=600  # Set timeout to 600 seconds
    )
    cursor = connection.cursor()
    # query = """
    #     SELECT COUNT(*) 
    #         FROM (
    #             SELECT 
    #                 ID, Name, Branch, Latitude, Longitude, Postal, Address,
    #                 (6371 * ACOS(
    #                     LEAST(1, GREATEST(-1, 
    #                         COS(RADIANS(33.590355)) * COS(RADIANS(Latitude)) * 
    #                         COS(RADIANS(Longitude) - RADIANS(130.401716)) + 
    #                         SIN(RADIANS(33.590355)) * SIN(RADIANS(Latitude))
    #                     ))
    #                 )) AS distance,
    #                 MATCH(Name, Branch, Address) AGAINST(%s IN BOOLEAN MODE) AS relevance_score
    #             FROM poitbl
    #             WHERE 
    #                 -- Broad fuzzy matching using BOOLEAN mode (using your NGRAM index)
    #                 MATCH(Name, Branch, Address) AGAINST(%s IN BOOLEAN MODE)
    #                 -- Refine results by requiring either a natural language match OR a regex match:
    #                 AND (
    #                     MATCH(Name, Branch, Address) AGAINST(%s IN NATURAL LANGUAGE MODE)
    #                     OR (Name RLIKE %s OR Branch RLIKE %s OR Address RLIKE %s OR Postal IN %s)
    #                 )
    #             -- Optional: You may remove ORDER BY in a COUNT query for speed.
    #             ORDER BY relevance_score DESC, distance ASC
    #         ) AS search_results;

    #     """
    # query = """
    #     create or replace view search_view as
    #         SELECT 
    #                 ID, Name, Branch, Latitude, Longitude, Postal, Address,
    #                 (6371 * ACOS(
    #                     LEAST(1, GREATEST(-1, 
    #                         COS(RADIANS(33.590355)) * COS(RADIANS(Latitude)) * 
    #                         COS(RADIANS(Longitude) - RADIANS(130.401716)) + 
    #                         SIN(RADIANS(33.590355)) * SIN(RADIANS(Latitude))
    #                     ))
    #                 )) AS distance,
    #                 MATCH(Name, Branch, Address) AGAINST(%s IN BOOLEAN MODE) AS relevance_score
    #             FROM poitbl
    #             WHERE 
    #                 -- Broad fuzzy matching using BOOLEAN mode (using your NGRAM index)
    #                 MATCH(Name, Branch, Address) AGAINST(%s IN BOOLEAN MODE)
    #                 -- Refine results by requiring either a natural language match OR a regex match:
    #                 AND (
    #                     MATCH(Name, Branch, Address) AGAINST(%s IN NATURAL LANGUAGE MODE)
    #                     OR (Name RLIKE %s OR Branch RLIKE %s OR Address RLIKE %s OR Postal IN %s)
    #                 )
    #             -- Optional: You may remove ORDER BY in a COUNT query for speed.
    #             ORDER BY relevance_score DESC, distance ASC"""
    query = """
        SELECT 
            ID, Name, Branch, Latitude, Longitude, Postal, Address,
            (6371 * ACOS(
                LEAST(1, GREATEST(-1, 
                    COS(RADIANS(39.989478333)) * COS(RADIANS(Latitude)) * 
                    COS(RADIANS(Longitude) - RADIANS(139.947610833)) + 
                    SIN(RADIANS(39.989478333)) * SIN(RADIANS(Latitude))
                ))
            )) AS distance,
            MATCH(Name, Branch, Address) AGAINST(%s IN natural language MODE) AS relevance_score
        FROM poitbl
        WHERE 
            MATCH(Name, Branch, Address) AGAINST(%s IN natural language MODE)
            AND (
                MATCH(Name, Branch, Address) AGAINST(%s IN natural language  MODE)
                OR (Name RLIKE %s OR Branch RLIKE %s OR Address RLIKE %s OR Postal IN %s)
            )
        ORDER BY relevance_score ASC, distance ASC;
    """
    cursor.execute(query, (natural_query, natural_query, natural_query, rlike_query, rlike_query, rlike_query, postal_query))
    results = cursor.fetchall()
    end_time = time.time()
    print(f"Search completed in {((end_time - start_time)*1000):.2f} milliseconds")
    print(len(results))

zen = "三春台"
search(zen)


# print("" in "abc")





# const searchButton = document.getElementById("searchButton");

# searchButton.addEventListener("click", async () => {
#     const startTime = performance.now(); // Start timer

#     // Trigger the search (Assuming using fetch API to call backend)
#     const response = await fetch('/search', {
#         method: 'POST',
#         body: JSON.stringify({ query: "your search query" }),
#         headers: { 'Content-Type': 'application/json' }
#     });

#     const data = await response.json();

#     // Render results here (pseudo-code)
#     renderResults(data);

#     const endTime = performance.now(); // End timer

#     // Calculate and log the time taken
#     const timeElapsed = (endTime - startTime).toFixed(2);
#     console.log(`Search completed in ${timeElapsed} ms`);

#     // Optionally show it to the user
#     document.getElementById("timeDisplay").innerText = `Search took ${timeElapsed} ms`;
# });
