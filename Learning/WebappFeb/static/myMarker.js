class myMarkerCluster {
    constructor(map, options = {}) {
        this.map = map;
        this.options = Object.assign({
            maxZoom: 15,
            radius: 50,
            method: 'distance',
            markers: [],
            cluster: (size, position) => this.defaultCluster(size, position)
        }, options);
        this.markers = [];
        this.clusters = [];
        ZDC.addListener(map, ZDC.MAP_CHG_ZOOM, () => this.redraw());
        ZDC.addListener(map, ZDC.MAP_DRAG_END, () => this.redraw());
        this.redraw();
    }

    addMarkers(markers) {
        this.markers.push(...markers);
        this.redraw();
    }

    clearClusters() {
        this.clusters.forEach(cluster => {
            try {
                this.map.removeWidget(cluster);
            } catch (error) {
                console.error('Error removing cluster:', error);
            }
        });
        this.clusters = [];
    }

    clusterMarkers() {
        let clustered = new Set();
        this.markers.forEach(marker => {
            if (clustered.has(marker)) return;
            let clusterGroup = this.markers.filter(m => this.getDistance(marker, m) < this.options.radius);
            this.createCluster(clusterGroup.length, this.getClusterPosition(clusterGroup));
            clusterGroup.forEach(m => clustered.add(m));
        });
    }

    getDistance(markerA, markerB) {
        const posA = markerA.getLatLon();
        const posB = markerB.getLatLon();
        
        // Using the Haversine formula for more accurate distance calculation
        const R = 6371; // Earth's radius in kilometers
        const dLat = (posB.lat - posA.lat) * Math.PI / 180;
        const dLon = (posB.lon - posA.lon) * Math.PI / 180;
        
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(posA.lat * Math.PI / 180) * Math.cos(posB.lat * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    getClusterPosition(markers) {
        let latSum = 0;
        let lonSum = 0;
        markers.forEach(m => {
            const pos = m.getLatLon();
            latSum += pos.lat;
            lonSum += pos.lon;
        });
        return new ZDC.LatLon(latSum / markers.length, lonSum / markers.length);
    }

    createCluster(count, position) {
        let sizes = [10, 100, 1000, 10000];
        let icons = ['../static/cluster1.png', '../static/cluster2.png', '../static/cluster3.png', '../static/cluster4.png', '../static/cluster5.png'];
        let index = sizes.findIndex(size => count < size);
        let icon = icons[index !== -1 ? index : icons.length - 1];
        let size = 30 + index * 2;

        let labelHTML = `
            <div style="width:${size}px;height:${size}px;line-height:${size}px;text-align:center;
            font-family:arial;color:#FFF;font-size:18px;font-weight:bold;
            background:url(${icon}) no-repeat center;background-size:contain;cursor:pointer;user-select:none;">
            ${count}</div>`;

        let widgetlabel = {
            html: labelHTML,
            size: new ZDC.WH(size, size),
            offset: new ZDC.Pixel(-size / 2, -size / 2)
        };

        let widget = new ZDC.UserWidget(position, widgetlabel);
        ZDC.addListener(widget, ZDC.MARKER_CLICK, () => {
            this.map.moveLatLon(position);
            this.map.setZoom(Math.min(this.map.getZoom() + 2, this.options.maxZoom));
        });
        this.clusters.push(widget);
        this.map.addWidget(widget);
    }

    redraw() {
        this.clearClusters();
        this.clusterMarkers();
    }
}
