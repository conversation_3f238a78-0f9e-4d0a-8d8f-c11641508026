body, html {
margin: 0;
padding: 0;
height: 100%;
width: 100vw;
font-family: 'Roboto', sans-serif;
background-color: #f4f4f9;
}

main {
display: flex;
height: 100%;
flex-direction: column;
position: relative;
}
#Zmap {
flex-grow: 1;
transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
width: 100vw;
height: 100vh;
}
#toggleBtn {
position: absolute;
margin: 0;
padding: 10px 15px;
left: 10px;
top: 5px;
z-index: 1001;
background-color: #4a90e2;
color: white;
border: none;
border-radius: 5px;
cursor: pointer;
box-shadow: 0 2px 8px rgba(0,0,0,0.15);
transition: left 0.5s ease-in-out, transform 0.3s ease;
}
#search_and_result.open ~ #toggleBtn {
left: 20px; 
}

#toggleBtn:hover {
transform: translateY(-2px);
background-color: #357abd;
box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

#toggleBtn.active {
transform: rotate(360deg); 
}

#search_and_result {
display: block;
width: 0;
max-width: 20%;
padding-top: 40px;
transition: width 0.5s ease-in-out;
background-color: #ffffff;
box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1); /* Shadow on the right */
overflow: hidden;
z-index: 1000;
position: absolute;
left: 0;  /* Move to the left */
top: 0;
height: calc(100% - 40px);
}


#search_and_result.open {
width: 20%;
}

#Circle {
position: absolute;
top: 0px;
right: 0px;  /* Move to the left */
z-index: 1001;
display: flex;
gap: 7px;
align-items: center;
padding: 5px;
border-radius: 12px;
}


#Circle-Radius {
width: 33px;
padding: 12px;
border: 2px solid #e1e4e8;
border-radius: 8px;
font-size: 14px;
transition: border-color 0.3s ease;
}

#Circle-Radius:focus {
border-color: #4a90e2;
outline: none;
}

#Circle-button {
padding: 12px 20px;
border: none;
border-radius: 8px;
background-color: #dc3545;
color: white;
font-weight: 600;
cursor: pointer;
transition: all 0.3s ease;
}

#Circle-button:hover {
transform: translateY(-2px); 
box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

#Circle-button.active {
background-color: #28a745;
}

#searchInput {
width: calc(100% - 20px);
margin: 10px 10px;
padding: 12px 15px;
border: 2px solid #e1e4e8;
border-radius: 10px;
font-size: 15px;
transition: all 0.3s ease;
}

#searchInput:focus {
border-color: #4a90e2;
outline: none;
box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

#searchBtn {
padding: 12px 10px;
margin-left: 10px;
border: none;
border-radius: 8px;
background-color: #4a90e2;
color: white;
font-weight: 600;
cursor: pointer;
transition: all 0.3s ease;
}

#searchBtn:hover {
background-color: #357abd;
}

#results {
padding: 10px;
overflow-y: auto;
height: calc(100% - 100px);
}

#results::-webkit-scrollbar {
width: 10px;
}

#results::-webkit-scrollbar-thumb {
background-color: #4a90e2;
border-radius: 10px;
}

#results::-webkit-scrollbar-track {
background-color: #f8f9fa;
}

#resultlist {
list-style-type: none;
padding: 0;
}

#results ul {
padding: 0;
margin: 0;
}

#resultsList li {
list-style-type: none;
padding: 15px;
margin-bottom: 10px;
border-radius: 10px;
background-color: #f8f9fa;
transition: all 0.3s ease;
}

#resultsList li:hover {
transform: translateY(-2px);
background-color: #e3e1e1;
box-shadow: 0 4px 15px rgba(0,0,0,0.05);
border-left: 4px solid #4a90e2;
}

/* Mobile Adjustments */
@media screen and (max-width: 768px) {
#search_and_result {
  max-width: 40%;
}

#search_and_result.open {
  width: 40%;
}
}