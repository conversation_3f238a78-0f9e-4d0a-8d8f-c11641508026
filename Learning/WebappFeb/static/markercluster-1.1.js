(function(){var d=window.ZDC;d.MarkerCluster=d.MarkerCluster=function(e,t){function r(a,b){for(var c in b)try{b[c].constructor==Object?a[c]=r(a[c],b[c]):b.hasOwnProperty(c)&&(a[c]=b[c])}catch(f){a[c]=b[c]}return a}this.indexOf=function(a,b,c){c=c||0;for(var g=a.length;c<g;c++)if(a[c]===b)return c;return-1};this.miriLon=e.getMapType().latLonPerPx.lon;this.miriLat=e.getMapType().latLonPerPx.lat;this.clusters=[];this.markers=[];this.markerNo=0;this.options={markers:[],maxZoom:e.getMapType().zoomRange.length-
1,cluster:function(a,g){var c=36,f="clusterermarker1.png",h="font-family:arial; color:#FFFFFF; font-size:20px; font-weight: bold;";5<=a&&(c=40,f="clusterermarker2.png",h="font-family:arial; color:#FFFFFF; font-size:22px; font-weight: bold;");10<=a&&(c=44,f="clusterermarker3.png",h="font-family:arial; color:#FFFFFF; font-size:22px; font-weight: bold;");50<=a&&(c=48,f="clusterermarker4.png",h="font-family:arial; color:#FFFFFF; font-size:24px; font-weight: bold;");100<=a&&(c=58,f="clusterermarker5.png",
h="font-family:arial; color:#FFFFFF; font-size:24px ;font-weight: bold;");f=d._MAP_SERVER_IMG+f;c={html:'<div style="width: '+c+"px;height: "+c+"px;line-height: "+c+"px;text-align: center;"+(b.options.textStyle||h)+"background: url("+f+') no-repeat center;cursor: pointer;user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;">'+a+"</div>",size:new d.WH(c,c),offset:d.Pixel(-c/2,-c/2),propagation:!0};return new d.UserWidget(g,c)},textStyle:null,method:"distance",
radius:10};r(this.options,t);this.clusters=[];var b=this;this.addClusterEventHandle=function(a){for(var b=[],c=0;c<a.markers.length;c++)b.push(a.markers[c].getLatLon());var f=e.getAdjustZoom(b,{fix:!1});d.addListener(a.owner,d.USERWIDGET_MOUSEDOWN,function(){e.moveLatLon(f.latlon);e.setZoom(f.zoom)})};this.kmeans={mkIdx:[],getMarkerInBox:function(){this.mkIdx=[];e.getLatLonBox();for(var a=0;a<b.markers.length;a++)b.markers[a].hidden(),this.mkIdx.push(a)},addCluster:function(){var a=e.getLatLonBox(),
g=a.getMin();a=a.getMax();for(var c=b.options,f=0;f<c.radius;f++){var h=(Math.random()*(a.lat-g.lat)+g.lat).toFixed(7),k=(Math.random()*(a.lon-g.lon)+g.lon).toFixed(7);h={owner:null,latlon:new d.LatLon(h,k),markers:[]};b.clusters.push(h)}},updateCluster:function(){for(var a=0;a<b.clusters.length;a++)b.clusters[a].markers=[];for(var g=0;g<this.mkIdx.length;g++){var c=Infinity,f=b.markers[this.mkIdx[g]];for(a=0;a<b.clusters.length;a++){var d=f.getLatLon().lat-b.clusters[a].latlon.lat,e=f.getLatLon().lon-
b.clusters[a].latlon.lon;d=Math.sqrt(Math.pow(d,2)+Math.pow(e,2));if(d<c){c=d;var l=b.clusters[a]}}l.markers.push(f);f.group=l}this.updateCenter()},updateCenter:function(){for(var a=!0,g=0;g<b.clusters.length;g++){var c=0,f=0,e=b.clusters[g];if(0<e.markers.length){for(var k=0;k<e.markers.length;k++){var l=e.markers[k].getLatLon();c+=parseFloat(l.lat);f+=parseFloat(l.lon)}c/=e.markers.length;f/=e.markers.length;c=c.toFixed(7);f=f.toFixed(7);if(e.latlon.lat!=c||e.latlon.lon!=f)a=!1;e.latlon=new d.LatLon(c,
f)}}a?this.drawCluster():this.updateCluster()},drawCluster:function(){if(0<b.clusters.length){for(var a=[],g=0;g<b.clusters.length;g++){var c=b.clusters[g];if(1<c.markers.length){var d=new b.options.cluster(c.markers.length,c.latlon);e.addWidget(d);d.open();c.owner=d;a.push(c);b.addClusterEventHandle(c)}else 1==c.markers.length&&c.markers[0].visible()}b.clusters=a}},cluster:function(){0<b.options.radius&&(this.getMarkerInBox(),this.addCluster(),this.updateCluster())},clear:function(){if(0<b.clusters.length){for(var a=
0;a<b.clusters.length;a++)null!=b.clusters[a]&&e.removeWidget(b.clusters[a].owner);b.clusters=[];if(0<b.markers.length)for(a=0;a<b.markers.length;a++)b.markers[a].visible()}}};this.distance={mkIdx:[],radius:0,getRarius:function(){this.radius=b.options.radius},getRelation:function(){this.mkIdx=[];if(0<b.markers.length){for(var a=0;a<b.markers.length;a++)b.markers[a].relateds=[],b.markers[a].inCluster=!1,this.mkIdx.push(a);for(a=0;a<this.mkIdx.length-1;a++)for(var g=b.markers[this.mkIdx[a]].getLatLon(),
c=a+1;c<this.mkIdx.length;c++){var d=b.markers[this.mkIdx[c]].getLatLon(),h=e.latLonToTL(g);d=e.latLonToTL(d);Math.round(Math.sqrt(Math.pow(h.top-d.top,2)+Math.pow(h.left-d.left,2)))<=this.radius&&(b.markers[this.mkIdx[a]].relateds.push(this.mkIdx[c]),b.markers[this.mkIdx[c]].relateds.push(this.mkIdx[a]))}}},createCluster:function(){this.mkIdx.sort(function(a,c){return b.markers[c].relateds.length==b.markers[a].relateds.length?b.markers[a].markerNo-b.markers[c].markerNo:b.markers[c].relateds.length-
b.markers[a].relateds.length});for(var a=null,d=0;d<this.mkIdx.length;d++)if(0<b.markers[this.mkIdx[d]].relateds.length&&0==b.markers[this.mkIdx[d]].inCluster){a=b.markers[this.mkIdx[d]];break}if(null!=a){d=new b.options.cluster(a.relateds.length+1,a.getLatLon());e.addWidget(d);d.open();d={owner:d,markers:[]};a.inCluster=!0;a.hidden();d.markers.push(a);for(var c=0;c<a.relateds.length;c++){var f=a.relateds[c];d.markers.push(b.markers[f]);b.markers[f].hidden();b.markers[f].inCluster=!0;for(var h=b.markers[f].relateds.length,
k=0;k<h;k++){var l=b.markers[f].relateds[k];if(!b.markers[l].inCluster){var n=b.indexOf(b.markers[l].relateds,f);b.markers[l].relateds.splice(n,1)}}}b.clusters.push(d);b.addClusterEventHandle(d);this.createCluster()}},cluster:function(){this.getRarius();this.getRelation();this.createCluster()},clear:function(){if(0<b.clusters.length){for(var a=0;a<b.clusters.length;a++)null!=b.clusters[a].owner&&e.removeWidget(b.clusters[a].owner);b.clusters=[];if(0<b.markers.length)for(a=0;a<b.markers.length;a++)b.markers[a].visible()}}};
this.clusterer={boundSize:{lat:0,lon:0},getBoundSize:function(){var a=e.getZoom(),g=b.options,c=b.miriLon;this.boundSize={lat:d.msTodeg(g.radius*b.miriLat[e.getMapType().zoomRange[a]]/2),lon:d.msTodeg(g.radius*c[e.getMapType().zoomRange[a]]/2)}},createCluster:function(){for(var a=0;a<b.markers.length;a++)b.markers[a].isClustering=!1;for(a=0;a<b.markers.length-1;a++){var g=b.markers[a];if(!g.isClustering){for(var c=g.getLatLon(),f=new d.LatLon(parseFloat(c.lat)-this.boundSize.lat,parseFloat(c.lon)+
this.boundSize.lon),h=new d.LatLon(parseFloat(c.lat)+this.boundSize.lat,parseFloat(c.lon)+this.boundSize.lon),k=new d.LatLon(parseFloat(c.lat)+this.boundSize.lat,parseFloat(c.lon)-this.boundSize.lon),l=new d.LatLon(parseFloat(c.lat)-this.boundSize.lat,parseFloat(c.lon)-this.boundSize.lon),n=[],q=a+1;q<b.markers.length;q++){var p=b.markers[q];if(!p.isClustering){var m=p.getLatLon();m=new d.LatLonBox(new d.LatLon(parseFloat(m.lat)-this.boundSize.lat,parseFloat(m.lon)-this.boundSize.lon),new d.LatLon(parseFloat(m.lat)+
this.boundSize.lat,parseFloat(m.lon)+this.boundSize.lon));if(d.getLineCrossRectLatLons(f,h,m)||d.getLineCrossRectLatLons(h,k,m)||d.getLineCrossRectLatLons(k,l,m)||d.getLineCrossRectLatLons(l,f,m))p.isClustering=!0,p.hidden(),n.push(p)}}0<n.length&&(n.push(g),c=new b.options.cluster(n.length,c),e.addWidget(c),c.open(),g.hidden(),g.isClustering=!0,g={owner:c,markers:n},b.clusters.push(g),b.addClusterEventHandle(g))}}},cluster:function(){this.getBoundSize();this.createCluster()},clear:function(){if(0<
b.clusters.length){for(var a=0;a<b.clusters.length;a++)null!=b.clusters[a].owner&&e.removeWidget(b.clusters[a].owner);b.clusters=[];if(0<b.markers.length)for(a=0;a<b.markers.length;a++)b.markers[a].visible()}}};d.MarkerCluster.prototype.addMarkers=d.MarkerCluster.prototype.addMarkers=this.addMarkers=function(a){for(var b=0;b<a.length;b++)a[b].markerNo=this.markerNo,e&&e.addWidget(a[b]),this.markers.push(a[b]),this.markerNo++};d.MarkerCluster.prototype.setMethod=d.MarkerCluster.prototype.setMethod=
this.setMethod=function(a){a!=this.options.method&&(this.options.method=a)};d.MarkerCluster.prototype.setRadius=d.MarkerCluster.prototype.setRadius=this.setRadius=function(a){a!=this.options.radius&&(this.options.radius=a)};d.MarkerCluster.prototype.setMaxZoom=d.MarkerCluster.prototype.setMaxZoom=this.setMaxZoom=function(a){a!=this.options.maxZoom&&(e.getZoom(),this.options.maxZoom=a)};d.MarkerCluster.prototype.getClusterMarker=d.MarkerCluster.prototype.getClusterMarker=this.getClusterMarker=function(){return this.clusters};
d.MarkerCluster.prototype.getMaxZoom=d.MarkerCluster.prototype.getMaxZoom=this.getMaxZoom=function(){return this.options.maxZoom};d.MarkerCluster.prototype.redraw=d.MarkerCluster.prototype.redraw=this.redraw=function(){this.kmeans.clear();this.distance.clear();this.clusterer.clear();this.draw()};d.MarkerCluster.prototype.draw=d.MarkerCluster.prototype.draw=this.draw=function(){var a=e.getZoom();0>=this.options.radius||a>this.options.maxZoom||0>=this.markers.length||("k-means"==this.options.method&&
this.kmeans.cluster(),"distance"==this.options.method&&this.distance.cluster(),"box"==this.options.method&&this.clusterer.cluster())};d.MarkerCluster.prototype.removeMarker=d.MarkerCluster.prototype.removeMarker=this.removeMarker=function(){this.options.markers=[];for(var a=0;a<this.markers.length;a++)e.removeWidget(this.markers[a]);this.markers=b.markers=[];this.redraw()};this.options.markers&&0<this.options.markers.length&&(this.addMarkers(this.options.markers),this.redraw());d.addListener(e,d.MAP_CHG_ZOOM,
function(){b.redraw()})}})();