(function() {
    let map, markers = [];
    let searchResults = [];
    let currentPage = 1;
    const resultsPerPage = 20;
    let searchHistory = JSON.parse(localStorage.getItem('searchHistory')) || [];
  
    // Load the map
    function loadMap() {
      if (typeof ZDC === 'undefined') {
        console.error("いつもNAVI API failed to load.");
        return;
      }
      const latlon = new ZDC.LatLon(35.6778614, 139.7703167);
      map = new ZDC.Map(document.getElementById('Map'), {
        latlon: latlon,
        zoom: 9,
        mapType: ZDC.MAPTYPE_HIGHRES_LV18
      });
      map.addWidget(new ZDC.ScaleBar());
      map.addWidget(new ZDC.Control({
        pos: { bottom: 25, left: 10 },
        type: ZDC.CTRL_TYPE_NORMAL,
        close: true
      }));
    }
  
    // Clear existing markers
    function clearMarkers() {
      markers.forEach(marker => map.removeWidget(marker));
      markers = [];
    }
  
    // Fetch and display search results
    async function searchForResults() {
      const query = document.getElementById('query').value.trim();
      if (!query) return;
  
      updateSearchHistory(query);
      currentPage = 1;
      try {
        const response = await fetch(`/search?q=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        searchResults = await response.json();
        toggleResultsVisibility(true);
        clearMarkers();
        searchResults.forEach(item => {
          const latlon = new ZDC.LatLon(item.latitude, item.longitude);
          const marker = new ZDC.Marker(latlon);
          markers.push(marker);
          map.addWidget(marker);
        });
        displayResults();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  
    // Display search results with pagination
    function displayResults() {
      const resultsList = document.getElementById('results-list');
      const pagination = document.getElementById('pagination');
      const toggleBtn = document.getElementById('toggle');
      toggleBtn.textContent = `▼Results (${searchResults.length})`;
  
      resultsList.innerHTML = '';
      pagination.innerHTML = '';
  
      const start = (currentPage - 1) * resultsPerPage;
      const end = Math.min(start + resultsPerPage, searchResults.length);
      const paginatedResults = searchResults.slice(start, end);
  
      paginatedResults.forEach((item, index) => {
        const li = document.createElement('li');
        li.setAttribute('data-index', start + index);
        li.innerHTML = `
          <div>
            <strong>${item.name} ${item.branch}</strong>
            <p>${item.postal} ${item.address}</p>
            <small>${item.latitude}, ${item.longitude}</small>
          </div>`;
        li.addEventListener('click', () => {
          map.setLatLon(new ZDC.LatLon(item.latitude, item.longitude));
          map.setZoom(15);
        });
        resultsList.appendChild(li);
      });
  
      displayPagination();
    }
  
    // Display pagination controls
    function displayPagination() {
      const pagination = document.getElementById('pagination');
      pagination.style.display = searchResults.length > resultsPerPage ? 'block' : 'none';
      const totalPages = Math.ceil(searchResults.length / resultsPerPage);
  
      const prevButton = document.createElement('button');
      prevButton.textContent = 'Prev';
      prevButton.disabled = currentPage === 1;
      prevButton.addEventListener('click', () => {
        currentPage--;
        displayResults();
      });
      pagination.appendChild(prevButton);
  
      const pageIndicator = document.createElement('span');
      pageIndicator.textContent = `Page ${currentPage} of ${totalPages}`;
      pagination.appendChild(pageIndicator);
  
      const nextButton = document.createElement('button');
      nextButton.textContent = 'Next';
      nextButton.disabled = currentPage === totalPages || totalPages === 0;
      nextButton.addEventListener('click', () => {
        currentPage++;
        displayResults();
      });
      pagination.appendChild(nextButton);
    }
  
    // Update search history
    function updateSearchHistory(query) {
      if (!searchHistory.includes(query)) {
        searchHistory.unshift(query);
        searchHistory = searchHistory.slice(0, 10);
        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
      }
    }
  
    // Toggle results visibility
    function toggleResultsVisibility(show) {
      const resultsDiv = document.getElementById('results');
      const toggleBtn = document.getElementById('toggle');
      if (show) {
        resultsDiv.style.display = 'block';
        toggleBtn.textContent = `▲Results (${searchResults.length})`;
      } else {
        resultsDiv.style.display = 'none';
        toggleBtn.textContent = `▼Results (${searchResults.length})`;
      }
    }
  
    // Event Listeners
    document.getElementById('toggle').addEventListener('click', () => {
      const resultsDiv = document.getElementById('results');
      const toggleBtn = document.getElementById('toggle');
      if (resultsDiv.style.display === 'none' || resultsDiv.style.display === '') {
      resultsDiv.style.display = 'block';
      toggleBtn.textContent = `▲Results (${searchResults.length})`;
      } else {
      resultsDiv.style.display = 'none';
      toggleBtn.textContent = `▼Results (${searchResults.length})`;
      }
    });
    document.getElementById('searchBtn').addEventListener('click', searchForResults);
  
    document.getElementById('query').addEventListener('keypress', event => {
      if (event.key === 'Enter') searchForResults();
    });
  
    let debounceTimeout;
    document.getElementById('query').addEventListener('input', () => {
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => {
        const query = document.getElementById('query').value.trim();
        if (query) {
          fetchSuggestions(query);
        }
      }, 300);
    });
  
    // Initialize map on load
    window.onload = function() {
      setTimeout(loadMap, 1000);
    };
  })();
  