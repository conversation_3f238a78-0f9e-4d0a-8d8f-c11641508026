import csv
import mysql.connector as MySQL
import unicodedata
from pyproj import Transformer

# Initialize coordinate transformer
transformer = Transformer.from_crs("EPSG:4301", "EPSG:4326", always_xy=True)

def changeType(cords) -> float:
    """
    Convert a coordinate string in DMS format to decimal degrees.
    """
    try:
        sign = 1 if cords[0] == '+' else -1
        parts = cords[1:].split(':')
        degrees = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        decimal = degrees + (minutes / 60) + (seconds / 3600)
        return sign * decimal
    except Exception as e:
        print(f"Error converting coordinates: {e}")
        return None

def location(row) -> list:
    """
    Process a row of data, convert coordinates, and normalize text.
    """
    try:
        lon_decimal = changeType(row[-3])
        lat_decimal = changeType(row[-4])
        if lon_decimal is None or lat_decimal is None:
            return None

        # Convert coordinates to WGS84
        lat_wgs84, lon_wgs84 = transformer.transform(lat_decimal, lon_decimal)

        # Construct the new row
        new_row = row[:-4] + [lat_wgs84, lon_wgs84] + row[-2:]

        # Normalize text fields (skip non-string fields)
        for i in range(len(new_row)):
            if isinstance(new_row[i], str):
                new_row[i] = unicodedata.normalize("NFKC", new_row[i])

        return new_row
    except Exception as e:
        print(f"Error processing row: {e}")
        return None

def main(file_path):
    """
    Main function to read, process, and insert data into the database.
    """
    try:
        # Connect to the database
        mydb = MySQL.connect(host='localhost', user='admin', password='password', database='testingdb')
        mycursor = mydb.cursor()

        # Drop and create the table (uncomment if needed)
        # mycursor.execute("DROP TABLE IF EXISTS TBLTest")
        # mycursor.execute("""
        #     CREATE TABLE IF NOT EXISTS TBLTest (
        #         ID INT AUTO_INCREMENT PRIMARY KEY,
        #         Name VARCHAR(255), 
        #         Branch VARCHAR(255), 
        #         Altname VARCHAR(255), 
        #         Altbranch VARCHAR(255), 
        #         Latitude DECIMAL(10, 8), 
        #         Longitude DECIMAL(11, 8),
        #         Postal VARCHAR(255),
        #         Address VARCHAR(255)
        #     )
        # """)

        # Read and process the TSV file
        with open(file_path, 'r', encoding="shift-jis", errors="ignore") as file:
            tsv_reader = csv.reader(file, delimiter='\t')
            for i, row in enumerate(tsv_reader, start=1):
                if i > 20000:  # Limit to 20,000 rows for testing
                    break

                # Process the row
                correct_row = location(row)
                if correct_row is None:
                    print(f"Skipping row {i} due to invalid data: {row}")
                    continue

                # Insert the row into the database
                try:
                    mycursor.execute("""
                        INSERT INTO TBLTest (Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, correct_row)
                    mydb.commit()
                except MySQL.Error as e:
                    print(f"Error inserting row {i}: {e}")

    except MySQL.Error as e:
        print(f"Error connecting to MySQL: {e}")
    except Exception as e:
        print(f"Error in main function: {e}")
    finally:
        if mydb.is_connected():
            mycursor.close()
            mydb.close()
            print("Database connection closed.")

if __name__ == '__main__':
    file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/WebappFeb/addresspoi.tsv'
    main(file_path)