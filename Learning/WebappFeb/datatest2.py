import csv
import mysql.connector as MySQL
import unicodedata

def changeType(cords) -> float:
    """ Convert DMS (Degrees, Minutes, Seconds) format to Decimal Degrees. """
    try:
        sign = 1 if cords[0] == '+' else -1
        parts = cords[1:].split(':')
        degrees = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        decimal = degrees + (minutes / 60) + (seconds / 3600)
        return sign * decimal
    except Exception:
        return None  # Return None if parsing fails

def location(data) -> list:
    """ Process and normalize location data. """
    if len(data) == 8:
        name, branch, altname, altbranch, lat_dms, lon_dms, postal, address = data
    elif len(data) == 6:
        name, altname, lat_dms, lon_dms, postal, address = data
        branch, altbranch = "", ""
    
    lat, lon = changeType(lat_dms), changeType(lon_dms)
    
    if lat is None or lon is None:
        print(f"Skipping row with invalid coordinates: {data}")
        return None
    
    # Normalize all strings to full-width Katakana for relevant fields
    name = unicodedata.normalize("NFKC", name) if isinstance(name, str) else name
    branch = unicodedata.normalize("NFKC", branch) if isinstance(branch, str) else branch
    altname = unicodedata.normalize("NFKC", altname) if isinstance(altname, str) else altname
    altbranch = unicodedata.normalize("NFKC", altbranch) if isinstance(altbranch, str) else altbranch
    postal = unicodedata.normalize("NFKC", postal) if isinstance(postal, str) else postal
    address = unicodedata.normalize("NFKC", address) if isinstance(address, str) else address

    
    return [name, branch, altname, altbranch, lat, lon, postal, address]

def main(file_path):
    try:
        mydb = MySQL.connect(
            host='localhost',
            user='admin',
            password='password'
        )
        print("Connected to MySQL successfully.")
        mycursor = mydb.cursor()
        
        # Create database and table with utf8mb4 charset and collation
        mycursor.execute("""CREATE DATABASE IF NOT EXISTS poidb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;""")
        mycursor.execute("USE poidb;")

        mycursor.execute("""
        CREATE TABLE IF NOT EXISTS poitbl (
            ID INT AUTO_INCREMENT PRIMARY KEY,
            Name VARCHAR(255) NOT NULL,
            Branch VARCHAR(255) DEFAULT NULL,
            Altname VARCHAR(255) NOT NULL,
            Altbranch VARCHAR(255) DEFAULT NULL,
            Latitude DECIMAL(10,8) NOT NULL,
            Longitude DECIMAL(11,8) NOT NULL,
            Postal VARCHAR(20) DEFAULT NULL,
            Address VARCHAR(255) DEFAULT NULL,
            location POINT NOT NULL,
            FULLTEXT INDEX ft_search (Altname, Altbranch, Postal) WITH PARSER ngram
        ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        """)
        print("Table created successfully.")

        # Create Spatial index on location
        mycursor.execute("ALTER TABLE poitbl ADD SPATIAL INDEX sp_idx_location (location);")
        print("Spatial index created successfully.")

        with open(file_path, 'r', encoding="shift_jis", errors="ignore") as file:
            tsv_reader = csv.reader(file, delimiter='\t')
            query = """INSERT INTO poitbl (Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address, location) 
                       VALUES (%s, %s, %s, %s, %s, %s, %s, %s, ST_GeomFromText(%s))"""
            
            batch_size = 1000  # Commit after every 1000 rows for better performance
            batch_data = []
            
            for i, row in enumerate(tsv_reader, start=1):
                # if i > 10:  # Limit for testing; remove in production
                #     break
                
                correct_row = location(row)
                if correct_row is None:
                    print(f"Skipping row {i}: {row}")
                    continue
                
                Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address = correct_row
                batch_data.append((
                    Name, Branch, Altname, Altbranch, Latitude, Longitude, Postal, Address, 
                    f'POINT({Longitude} {Latitude})'
                ))
                
                # Commit in batches
                if len(batch_data) >= batch_size:
                    mycursor.executemany(query, batch_data)
                    mydb.commit()
                    print(f"Batch of {len(batch_data)} rows inserted.")
                    batch_data = []  # Clear the batch
        
            # Commit any remaining data
            if batch_data:
                mycursor.executemany(query, batch_data)
                mydb.commit()
                print(f"Batch of {len(batch_data)} rows inserted.")

        print("Data insertion completed successfully.")

        # Updating the location column if not set during insertion (in case of missing data)
        mycursor.execute("""UPDATE poitbl 
            SET location = ST_GeomFromText(CONCAT('POINT(', Longitude, ' ', Latitude, ')'))
            WHERE location IS NULL;
        """)
        print("Location data updated successfully.")

    except MySQL.Error as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error in main function: {e}")
    finally:
        if mydb.is_connected():
            mycursor.close()
            mydb.close()

if __name__ == "__main__":
    file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/WebappFeb/addresspoi.tsv'
    main(file_path)
