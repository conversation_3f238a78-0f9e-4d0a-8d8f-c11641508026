import csv
import mysql.connector as MySQL
# import unicodedata
# # from pyproj import Transformer

# # Coordinate transformation: Tokyo Datum (EPSG:4301) → JGD2011 (EPSG:4612)
# # transformer = Transformer.from_crs("EPSG:4301", "EPSG:4612", always_xy=True)

# def normalize_text(text: str) -> str:
#     """Normalize text using NFKC for consistent full-text search."""
#     return unicodedata.normalize("NFKC", text) if text else ""

# def dms_to_decimal(dms: str) -> float:
#     """
#     Convert DMS string (e.g., "+35:41:22.140" or "35:41:22.140N") 
#     to decimal degrees.
#     """
#     try:
#         # Check for hemisphere prefix (N/S/E/W)
#         hemisphere = dms[0] if dms[0] in ('N', 'S', 'E', 'W') else None
#         if hemisphere:
#             dms = dms[1:]  # Remove hemisphere letter
#             sign = 1 if hemisphere in ('N', 'E') else -1
#         else:
#             sign = -1 if dms.startswith('-') else 1
#             dms = dms[1:] if dms.startswith(('+', '-')) else dms

#         parts = list(map(float, dms.split(':')))
#         degrees = parts[0] + parts[1]/60 + parts[2]/3600
#         return sign * degrees
#     except Exception as e:
#         print(f"Error parsing DMS '{dms}': {e}")
#         return None

# def process_row(row: list) -> list:
#     """Process a TSV row into normalized database fields."""
#     if len(row) == 8:
#         name, branch, altname, altbranch, lat_dms, lon_dms, postal, address = row
#     elif len(row) == 6:
#         name, altname, lat_dms, lon_dms, postal, address = row
#         branch, altbranch = "", ""
#     else:
#         print(f"Skipping malformed row: {row}")
#         return None

#     # Convert coordinates
#     lat = dms_to_decimal(lat_dms.strip())
#     lon = dms_to_decimal(lon_dms.strip())
#     if lat is None or lon is None:
#         return None

#     # Transform coordinates (Tokyo → JGD2011)
#     # try:
#     #     lon, lat = transformer.transform(lon, lat)  # Input order: x (lon), y (lat)
#     # except Exception as e:
#     #     print(f"Coordinate transform failed: {e}")
#     #     return None

#     # Normalize all text fields
#     return [
#         normalize_text(name),
#         normalize_text(branch),
#         normalize_text(altname),
#         normalize_text(altbranch),
#         lat,
#         lon,
#         normalize_text(postal),
#         normalize_text(address)
#     ]

# def main(file_path):
#     try:
#         # Connect with UTF-8 settings
#         mydb = MySQL.connect(
#             host='localhost',
#             user='admin',
#             password='password',
#             database='testingDB',
#             charset='utf8mb4'
#         )
#         mycursor = mydb.cursor()

#         # Create table with FULLTEXT index
#         mycursor.execute("DROP TABLE IF EXISTS testTBL")
#         mycursor.execute("""
#             CREATE TABLE testTBL (
#                 ID INT AUTO_INCREMENT PRIMARY KEY,
#                 name VARCHAR(255) NOT NULL, 
#                 branch VARCHAR(255), 
#                 altname VARCHAR(255) NOT NULL, 
#                 altbranch VARCHAR(255), 
#                 latitude DECIMAL(10, 8) NOT NULL, 
#                 longitude DECIMAL(11, 8) NOT NULL,
#                 postal VARCHAR(255) NOT NULL,
#                 address VARCHAR(255) NOT NULL,
#                 FULLTEXT INDEX idx_ft (altname, altbranch) WITH PARSER ngram
#             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
#         """)

#         # Batch insert data
#         batch_size = 1000
#         batch = []

#         with open(file_path, 'r', encoding="shift_jis", errors="ignore") as file:
#             tsv_reader = csv.reader(file, delimiter='\t')
#             for i, row in enumerate(tsv_reader, start=1):
#                 processed = process_row(row)
#                 if processed:
#                     batch.append(processed)
                
#                 # Insert in batches
#                 if len(batch) >= batch_size:
#                     mycursor.executemany(
#                         "INSERT INTO testTBL (name, branch, altname, altbranch, latitude, longitude, postal, address) "
#                         "VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
#                         batch
#                     )
#                     batch = []

#             # Insert remaining rows
#             if batch:
#                 mycursor.executemany(
#                     "INSERT INTO testTBL (name, branch, altname, altbranch, latitude, longitude, postal, address) "
#                     "VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
#                     batch
#                 )

#         mydb.commit()
#         print(f"Inserted {i} rows successfully.")

#     except Exception as e:
#         print(f"Error: {e}")
#         mydb.rollback()
#     finally:
#         if mydb.is_connected():
#             mycursor.close()
#             mydb.close()

# if __name__ == "__main__":
#     file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/WebappFeb/addresspoi.tsv'
#     main(file_path)
# file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/WebappFeb/addresspoi.tsv'


# mydb = MySQL.connect(
#     host='localhost',
#     user = 'admin',
#     password = 'password',
#     database = 'smallDB',
#     charset = 'utf8mb4'
# )
# mycursor = mydb.cursor()
# mycursor.execute("select * from testTBL limit 10")
# result = mycursor.fetchall()
# for row in result:
#     print(row)

print(int(True))  # prints 1 for True
print(int(False))  # prints 0 for False
