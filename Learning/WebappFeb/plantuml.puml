@startuml
participant User
participant "Frontend (JS)" as Frontend
participant "Backend (Flask API)" as Backend
participant "Database (MySQL)" as DB
participant "Map (ZDC API)" as Map

User -> Frontend: 検索キーワード入力
Frontend -> Backend: GET /search?q=query使用して検索
Backend -> DB: 検索キーワードに一致する場所を検索
DB --> Backend: 一致する場所を返す
Backend --> Frontend: 検索結果を返す（JSON）
Frontend -> Frontend: 検索履歴を更新
Frontend -> Frontend: リストで結果を表示

User -> Frontend: 検索結果をクリック
Frontend -> Map: 検索結果の場所に移動
Frontend -> Backend: GET /getMarkers（マップの境界）
Backend -> DB: 表示エリア内のマーカーをクエリ
DB --> Backend: マーカーデータを返す
Backend --> Frontend: マーカーデータを返す（JSON）
Frontend -> Map: マップにマーカーを追加

User -> Map: マップをズームまたはドラッグ
Map -> Frontend: マップの状態変更を検出
Frontend -> Backend: GET /getMarkers（更新された境界）
Backend -> DB: 新しいエリア内のマーカーをクエリ
DB --> Backend: 更新されたマーカーを返す
Backend --> Frontend: 新しいマーカーを返す（JSON）
Frontend -> Map: マップ上のマーカーを更新

@enduml
