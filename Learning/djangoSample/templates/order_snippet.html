<div class="col-md-12 mb-4">
    <h4 class="d-flex justify-content-between align-items-center mb-3">
    <span class="text-muted">Your cart</span>
    <span class="badge badge-secondary badge-pill">{{ order.items.count }}</span>
    </h4>
    <ul class="list-group mb-3 z-depth-1">
    {% for order_item in order.items.all %}
    <li class="list-group-item d-flex justify-content-between lh-condensed">
        <div>
        <h6 class="my-0">{{ order_item.quantity }} x {{ order_item.item.title}}</h6>
        <small class="text-muted">{{ order_item.item.description}}</small>
        </div>
        <span class="text-muted">${{ order_item.get_final_price }}</span>
    </li>
    {% endfor %}
    {% if order.coupon %}
    <li class="list-group-item d-flex justify-content-between bg-light">
        <div class="text-success">
        <h6 class="my-0">Promo code</h6>
        <small>{{ order.coupon.code }}</small>
        </div>
        <span class="text-success">-${{ order.coupon.amount }}</span>
    </li>
    {% endif %}
    <li class="list-group-item d-flex justify-content-between">
        <span>Total (USD)</span>
        <strong>${{ order.get_total }}</strong>
    </li>
    </ul>

    {% if DISPLAY_COUPON_FORM %}
    <form class="card p-2" action="{% url 'core:add-coupon' %}" method="POST">
        {% csrf_token %}
        <div class="input-group">
            {{ couponform.code }}
            <div class="input-group-append">
            <button class="btn btn-secondary btn-md waves-effect m-0" type="submit">Redeem</button>
            </div>
        </div>
    </form>
    {% endif %}

</div>

