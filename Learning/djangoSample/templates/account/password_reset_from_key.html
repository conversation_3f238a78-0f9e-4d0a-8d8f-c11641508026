{% extends "account/base.html" %}

{% load i18n %}
{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block content %}
    <h1>{% if token_fail %}{% trans "Bad Token" %}{% else %}{% trans "Change Password" %}{% endif %}</h1>

    {% if token_fail %}
        {% url 'account_reset_password' as passwd_reset_url %}
        <p>{% blocktrans %}The password reset link was invalid, possibly because it has already been used.  Please request a <a href="{{ passwd_reset_url }}">new password reset</a>.{% endblocktrans %}</p>
    {% else %}
        {% if form %}
            <form method="POST" action="{{ action_url }}">
                {% csrf_token %}
                {{ form.as_p }}
                <input type="submit" name="action" value="{% trans 'change password' %}"/>
            </form>
        {% else %}
            <p>{% trans 'Your password is now changed.' %}</p>
        {% endif %}
    {% endif %}
{% endblock %}
