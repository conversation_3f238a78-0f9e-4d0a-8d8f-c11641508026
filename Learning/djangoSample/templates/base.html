{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>{% block head_title %}{% endblock %}</title>
  {% block extra_head %}
  {% endblock %}
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css">
  <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/mdb.min.css' %}" rel="stylesheet">
  <link href="{% static 'css/style.min.css' %}" rel="stylesheet">
  <style type="text/css">
    html,
    body,
    header,
    .carousel {
      height: 60vh;
    }

    @media (max-width: 740px) {

      html,
      body,
      header,
      .carousel {
        height: 100vh;
      }
    }

    @media (min-width: 800px) and (max-width: 850px) {

      html,
      body,
      header,
      .carousel {
        height: 100vh;
      }
    }

  </style>
</head>

{% include "navbar.html" %}

<body>

    <div class="mt-5 pt-4">
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        {% endfor %}
    {% endif %}
    </div>

    {% block content %}
    {% endblock content %}

    {% block extra_body %}
    {% endblock %}

    {% include "footer.html" %}
    {% include "scripts.html" %}

    {% block extra_scripts %}
    {% endblock extra_scripts %}
</body>
</html>