/* Required height of parents of the Half Page Carousel for proper displaying carousel itself */
html,
body,
.view {
  height: 100%; }

/* Half Page Carousel itself*/
.carousel {
  height: 50%; }
  .carousel .carousel-inner {
    height: 100%; }
    .carousel .carousel-inner .carousel-item,
    .carousel .carousel-inner .active {
      height: 100%; }

/* Adjustment for mobile devices*/
@media (max-width: 776px) {
  .carousel {
    height: 100%; } }

/* Footer color for sake of consistency with Navbar */
.page-footer {
  background-color: #929FBA; }
