<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Map with Search</title>
  <script src="{{oauth_url}}" type="text/javascript"></script>
  <link rel="stylesheet" href="../static/style.css">
 
  <script type="text/javascript">
    let map, 
        lat = 35.6778614, lon = 139.7703167;

    function loadMap() {
        map = new ZDC.Map(
        document.getElementById('map'),
        {
            latlon: new ZDC.LatLon(lat, lon),
            zoom: 9,
            mapType: ZDC.MAPTYPE_HIGHRES_LV18
        });

        map.addWidget(new ZDC.ScaleBar());
        map.addWidget(new ZDC.Control());
    };

    function resizeMap() {
      if (map) {
          map.invalidateSize(); // Ensure the map resizes correctly
      }
  }

  // Load the map when the page loads
  window.addEventListener("load", function () {
      loadMap();
      resizeMap(); // Ensure correct initial size
  });

  // Listen for window resize to adjust the map
  window.addEventListener("resize", function () {
      resizeMap();
  });

  </script>
</head>
<body>
  <div id="map" style="width: 100%;">
    <div id="searchBox">
        <div id="searchbody">
            <input type="text" id="searchInput" placeholder="Type something..." autocomplete="off">
            <button id="searchButton">Search</button>
        </div>
        <div id="extras">
            <div id="suggestions" class="suggestions"></div>
            <hr>
            <div id="history" class="history">
                <h3 style="text-align: center;">History</h3>
                <ul id="historyList"></ul>
            </div>
        </div>
    </div>
    <div id="results">
        <button id="toggleButton" style="display: none;">▼ Show Results (0)</button>
        <div id="resultDetails"></div>
    </div>
  </div>
</body>
</html>
