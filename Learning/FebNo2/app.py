from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import getOauthURL  
import mysql.connector as Mysql

from pykakasi import kakasi
kks = kakasi()
def kana(text: str) -> str:
    result = kks.convert(text)
    converted_text = "".join([item["kana"] for item in result])
    return converted_text

app = Flask(__name__)
CORS(app)

@app.route("/")
def main():
    oauth_url = getOauthURL.getUrl()
    if not oauth_url.startswith("http"):
        return "Error: OAuth URL is invalid!", 500
    return render_template("index.html", oauth_url=oauth_url)
@app.route("/search", methods=["get"])
def searchresults():
    keywords = request.args.get("q", "").strip().lower()
    if not keywords:
        return jsonify([])
    try:
        keywords = [kana(keyword) for keyword in keywords]
        regexp_keyword = "|".join(keywords)
        fulltext_keyword = " ".join(keywords)
        fulltext_conditions = f"match(altname, altbranch, postal) against ('{fulltext_keyword}' in boolean mode)"
        regexp_conditions = f"(altname regexp '{regexp_keyword}' or altbranch regexp '{regexp_keyword}')"

        db = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="poidb",
        )   
        cursor = db.cursor()
        query = f"""SELECT * FROM poitbl WHERE {fulltext_conditions} AND {regexp_conditions}"""
        cursor.execute(query)
        results = cursor.fetchall()
        data = [
            {
                "id": row["ID"],
                "name": row["Name"],
                "branch": row["Branch"],
                "altname": row["Altname"],
                "altbranch": row["Altbranch"],
                "latitude": row["Latitude"],
                "longitude": row["Longitude"],
                "postal": row["Postal"],
                "address": row["Address"],
            }
            for row in results
        ]
        return jsonify(data)

    except Exception as e:
        return jsonify({"error": "Search failed. Please try again latitudeer."}), 500


@app.route('/saveHistory', methods=['POST'])
def save_history():
    data = request.get_json()
    name = data.get('name', '').strip()
    branch = data.get('branch', '').strip()
    altname = data.get('altname', '').strip()
    altbranch = data.get('altbranch', '').strip()
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    postal = data.get('postal')
    address = data.get('address')
    if not name:
        return jsonify({"error": "Name is required"}), 400

    try:
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="poidb"
        )
        cursor = conn.cursor()
        sql = """
        INSERT IGNORE INTO search_history (name, branch, altname, altbranch, latitude, longitude, postal, address) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
        """
        cursor.execute(sql, (name, branch, altname, altbranch, latitude, longitude, postal, address))
        conn.commit()
    except Mysql.Error as e:
        print(f"Error connecting to MySQL: {e}")  # Log error
        return jsonify({"error": "Database connection failed"}), 500  # Return error to client
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

    return jsonify({"message": "History saved successfully"})

@app.route('/getHistory', methods=['GET'])
def get_history():
    try:
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="poidb"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM search_history;")
        results = cursor.fetchall()

        data = [
            {
                "id": row[0],
                "name": row[1],
                "branch": row[2],
                "altname": row[3],
                "altbranch": row[4],
                "latitude": row[5],
                "longitude": row[6],
                "postal": row[7],
                "address": row[8]
            } for row in results
        ]
    except Mysql.Error as e:
        print(f"Error: {e}")
        return jsonify({"error": "Failed to fetch history"}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

    return jsonify(data)

@app.route('/deleteHistory', methods=['DELETE'])
def delete_history():
    data = request.get_json() 
    history_id = data.get('id')

    if not history_id:
        return jsonify({"error": "ID is required"}), 400  # If no ID is provided, return error

    try:
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="poidb"
        )
        cursor = conn.cursor()
        
        sql = "DELETE FROM search_history WHERE id = %s"
        cursor.execute(sql, (history_id,)) 
        conn.commit()

        if cursor.rowcount == 0: 
            return jsonify({"error": "History item not found"}), 404  # If no item is found with this id
        return jsonify({"message": "History deleted successfully"})  # Success response

    except Mysql.Error as e:
        print(f"Error: {e}")
        return jsonify({"error": "Failed to delete history"}), 500  # Database error

    finally:
        if 'cursor' in locals():
            cursor.close() 
        if 'conn' in locals():
            conn.close() 

if __name__ == '__main__':
    app.run(debug=True)
