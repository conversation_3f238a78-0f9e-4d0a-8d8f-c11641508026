body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
}
#searchbody {
    display: flex;
    z-index: 1000;
    position: absolute;
    top: 10px;
    left: 10px;
    width: 35%;
    cursor: pointer;
}

#searchInput {
    width: 100%;
}
#searchButton {
    background-color: #007BFF;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    padding: 10px;
    margin-left: 10px; 
    height: 40px;       
}
#map {
    height: calc(100vh - 150px);
    margin: 10px auto;
    width: 95%;
    border: 1px solid #ccc;
    border-radius: 10px;
    position: relative;
}

.suggestions {
    border: 1px solid #ccc;
    max-width: 310px;
    max-height: 180px;
    padding-left: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    display: none;
    overflow-y: auto;
    background-color: #ffffff;
    cursor: pointer;
    animation: slideDown 0.5s ease;
    margin-top: 17px;
    margin-left: 12px;
}

#results {
    z-index: 1000;
    position: absolute;
    top: 10px;
    right: 10px;
}

#resultDetails {
    border: 1px solid #ccc;
    padding-left: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    overflow-x: hidden;
    display: none;
    background-color: #ffffff;
    cursor: pointer;
    z-index: 1000;
    animation: slideDown 0.5s ease;
    width: 95%;
    padding-right: 10px;
    height: 400px;
}

#toggleButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    width: 120px;
    height: 40px;
    font-size: 12px;
    
}

#toggleButton:hover {
    background-color: #45a049;
}

input, button {
    margin: 10px 10px;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

button {
    background-color: #007BFF;
    color: white;
    border: none;
    cursor: pointer;
}

#history {
    position: relative;
    left: 10px;
    width: 300px;
    max-height: 50%;
    overflow-y: auto;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    background-color: #ffffff;
    display: none;
    padding: 10px;
    animation: slideDown 0.5s ease;

}

#history li {
    list-style: none;
    padding: 5px;
    margin-left: -20px;
    height: 50px;
    padding-right: 10px;
    border-bottom: 1px solid #ccc;
    display: flex;
    justify-content: space-between;
}
#extras {
    z-index: 1002;
    display: flex;
    flex-direction: column;
    margin-top: 50px;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

button:focus, input:focus {
    outline: 2px solid #007BFF;
}

@media (max-width: 480px) {
    #searchBox, #results {
        width: 90%;
        top: 10px;
        left: 5%;
    }

    #map {
        height: calc(100vh - 120px);
    }
}

@media (max-width: 768px) {
    #searchBox, #results {
        width: 90%;
        top: 10px;
        left: 5%;
    }

    #map {
        height: calc(100vh - 120px);
    }
}
