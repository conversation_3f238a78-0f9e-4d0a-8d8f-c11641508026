import csv
import mysql.connector as MySQL
import unicodedata
    
    
def changeType(cords) -> str:
    try:
        sign = 1 if cords[0] == '+' else -1
        parts = cords[1:].split(':')
        degrees = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        decimal = degrees + (minutes / 60) + (seconds / 3600)
        return sign * decimal
    except Exception as e:
        return False
def location(row) -> list:
    lon_decimal = changeType(row[-1])
    lat_decimal = changeType(row[-2])
    info = row[:-2]
    for i in range(len(info)):
        info[i] = unicodedata.normalize("NFKC", info[i])
    new_row = info + [lat_decimal, lon_decimal]
    return new_row


def main(file_path):
    mydb = MySQL.connect(host='localhost', user='admin', password='password')
    mycursor =mydb.cursor()
    mycursor.execute("use smalldb")

    # mycursor.execute("create database if not exists fulltextDB")
    # mycursor.execute("use fulltextDB")
    # mycursor.execute("DROP TABLE IF EXISTS FTtest")
    # mycursor.execute("""
    #     CREATE TABLE if not exists FTtest (
    #         ID INT AUTO_INCREMENT PRIMARY KEY,
    #         name VARCHAR(255), 
    #         place VARCHAR(255), 
    #         altname VARCHAR(255), 
    #         altplace VARCHAR(255), 
    #         lat DECIMAL(10, 8), 
    #         lon DECIMAL(11, 8),
    #         FULLTEXT(name, place)
    #     )
    # """)
    # mycursor.execute("ALTER TABLE FTtest ADD FULLTEXT (altname, altplace)")

    
    # output_file = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/Data&API/mysql/testing/error.tsv'
    with open(file_path, 'r', encoding="utf-8") as file:
        # with open (output_file, 'w', encoding="utf-8") as output:
            tsv_reader = csv.reader(file, delimiter='\t')
            # tsv_writer = csv.writer(output, delimiter='\t')
            for i, row in enumerate(tsv_reader, start=1):
                current_row = list(row)
                correct_row = location(current_row)
                if False in correct_row:
                    pass
                    # errors.append([i, current_row[0]])
                    # tsv_writer.writerow([i, current_row])
                else:
                    try:
                        if len(correct_row) == 6:
                            mycursor.execute("""
                                INSERT INTO testTBL (rname, rplace, altname, altplace, lat, lon)
                                VALUES (%s, %s, %s, %s, %s, %s)
                            """, correct_row)
                            mydb.commit()
                        elif len(correct_row) == 4:
                            mycursor.execute("""
                                INSERT INTO testTBL (rname, rplace, altname, altplace, lat, lon)
                                VALUES (%s, NULL, %s, NULL, %s, %s)
                            """, correct_row)
                            mydb.commit()
                    except MySQL.Error as e:
                        print(f"Error inserting row '{correct_row}': {i}")
    mydb.commit()
    print("Data inserted successfully.")

if __name__ == '__main__':
    file_path = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/Data&API/mysql/miniT.tsv'
    main(file_path)
    # noBlanks()