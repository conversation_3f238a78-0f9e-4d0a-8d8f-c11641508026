# # Convert the file to UTF-8
# from charset_normalizer import detect
# input_file = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/Webapp/test/711.tsv'
# output_file = 'c:/Users/<USER>/OneDrive/ドキュメント/Internship/Learning/Webapp/test/711_shift_jis.tsv'

# with open(input_file, 'rb') as file:
#     raw_data = file.read()
#     detected_encoding = detect(raw_data)['encoding']
#     print(f"Detected encoding: {detected_encoding}")

# # Read the file in the detected encoding and write it as Shift JIS
# with open(input_file, 'r', encoding=detected_encoding) as infile, open(output_file, 'w', encoding='shift_jis') as outfile:
#     content = infile.read()
#     outfile.write(content)

# print(f"File converted to Shift JIS and saved as {output_file}.")


# import unicodedata
# nfkc = unicodedata.normalize("NFKC", "ミッキー マウス")
# nfkd = unicodedata.normalize("NFKD", "ミッキー マウス")

# print(nfkc == nfkd)

# import pykakasi
# print(pykakasi.kakasi())



# TEXT = "seven sapporo 711"
# kana_query = TEXT.split()
# print(kana_query)
# space_kana_query = " ".join(kana_query)
# pipe_kana_query = "|".join(kana_query)
# print(space_kana_query)
# print(pipe_kana_query)
# print(q for q in kana_query)

# TEXT = "セブン サッポロ"
# kana_query = TEXT.split()
# print(kana_query)
# space_kana_query = " ".join(kana_query)
# pipe_kana_query = "|".join(kana_query)
# wildcard_query = [f"%{i}%" for i in kana_query]
# print(wildcard_query)
# print(space_kana_query)
# print(pipe_kana_query)
# print(q for q in kana_query)


# sql = "select * from testTbl where match(altname, altplace) against ('%s' in boolean mode) and (altname regexp '%s' or altplace regexp '%s') AND ((altname like '%s' and altplace like '%s') or (altname like '%s' and altplace like '%s');"


# from pyproj import Transformer

# # Define the coordinate systems
# transformer = Transformer.from_crs("EPSG:4301", "EPSG:4612", always_xy=True)

# def mapdata(latlon) -> list:
#     def changeType(cord):
#         try:
#             sign = 1 if cord[0] == '+' else -1
#             parts = cord[1:].split(':')
#             degrees = int(parts[0])
#             minutes = int(parts[1])
#             seconds = float(parts[2])
#             decimal = degrees + (minutes / 60) + (seconds / 3600)
#             return sign * decimal
#         except:
#             return None
#     lat_decimal = changeType(latlon[1])
#     lon_decimal = changeType(latlon[0])
#     if None in [lon_decimal, lat_decimal]:
#         return None, None

#     lon, lat = transformer.transform(lat_decimal, lon_decimal)
    
#     return [lat, lon]

# if __name__ == "__main__":
#     Latlon = ["+035:26:00.90", "+139:37:01.60"]
#     print(f"minamioota : {mapdata(Latlon)}")



import pykakasi

kks = pykakasi.kakasi()
def kana(text: str) -> str:
    result = kks.convert(text)
    converted_text = "".join([item["kana"] for item in result])
    return converted_text


# test = ["せぶん", "さぽろ"]
# test2 = [kana(t) for t in test]
# print(test2)

import mysql.connector

# MySQL Database Connection
db = mysql.connector.connect(
    host="localhost",
    user="admin",
    password="password",
    database="smallDB",
)
cursor = db.cursor()

def search_places(query):
    # Convert multi-word queries into multiple LIKE conditions
    keywords = query.split()  # Splitting by space to handle multiple words
    keywords = [kana(keyword) for keyword in keywords]
    regexp_keyword = "|".join(keywords)
    fulltext_keyword = " ".join(keywords)
    fulltext_conditions = f"match(altname, altbranch, postal) against ('{fulltext_keyword}' in boolean mode)"
    regexp_conditions = f"(altname regexp '{regexp_keyword}' or altbranch regexp '{regexp_keyword}')"

    query = f"""SELECT * FROM testTBL WHERE {fulltext_conditions} AND {regexp_conditions}"""
    print("query: "+"\n" + query)
    cursor.execute(query)
    results = cursor.fetchall()

    return results

if __name__ == "__main__":
    row = search_places("札幌")
    print(r for r in row)
    row2 = search_places("サッポ セブン")
    print(r for r in row2)


# # Close database connection
# cursor.close()
# db.close()


# def dms_to_decimal(dms: str) -> float:
#     """
#     Convert DMS string (e.g., "+35:41:22.140" or "35:41:22.140N") 
#     to decimal degrees.
#     """
#     try:
#         # Check for hemisphere prefix (N/S/E/W)
#         hemisphere = dms[0] if dms[0] in ('N', 'S', 'E', 'W') else None
#         if hemisphere:
#             dms = dms[1:]  # Remove hemisphere letter
#             sign = 1 if hemisphere in ('N', 'E') else -1
#         else:
#             sign = -1 if dms.startswith('-') else 1
#             dms = dms[1:] if dms.startswith(('+', '-')) else dms

#         parts = list(map(float, dms.split(':')))
#         degrees = parts[0] + parts[1]/60 + parts[2]/3600
#         return sign * degrees
#     except Exception as e:
#         print(f"Error parsing DMS '{dms}': {e}")
#         return None
    
# res = dms_to_decimal("+35:41:22.140")
# print(res)