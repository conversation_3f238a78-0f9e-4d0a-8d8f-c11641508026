<!DOCTYPE html>
<html lang="ja">
<head>
<meta charset="UTF-8">
<script src= "//test.api.its-mo.com/v3/loader?key=JSZ177163895ed5|F0HI1&api=zdcmapDisp.js,control.js&enc=UTF8"" type="text/javascript"></script>
<script type="text/javascript">

    let mapDisp, mrk,
        lat = 35.6778614, lon = 139.7703167;

    function loadmap() {
        mapDisp = new ZDC.MAP(
            document.getElementById('Zmap'),
            {
                latlon: new ZDC.LatLon(lat, lon),
                zoom: 9,
                mapDispType: ZDC.MAPTYPE_HIGHRES_LV18
            }
        );

        mapDisp.addWidget(new ZDC.ScaleBar());
        mapDisp.addWidget(new ZDC.Control());

        /* 地図をクリックしたときの動作 */
        ZDC.addListener(mapDisp, ZDC.MAP_CLICK, makeMarker);
    };
    // let cnt = 1;
    function makeMarker() {
        // if (cnt > 30) return;
        /* クリックした地点にマーカを作成 */
        mrk = new ZDC.Marker(mapDisp.getClickLatLon(),{
            color: ZDC.MARKER_COLOR_ID_BLUE_S,
            // number: ZDC['MARKER_NUMBER_ID_' + cnt + '_S']
        });
        // cnt++;
        /* マーカを追加 */
        mapDisp.addWidget(mrk);
    };
</script>

</head>

<body onload="loadmap();">
    <div id="Zmap" style="border:1px solid #777777; width:90%; height:500px; margin: auto; position:relative;"></div>
</body>
</html>