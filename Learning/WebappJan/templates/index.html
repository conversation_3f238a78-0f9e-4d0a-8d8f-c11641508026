<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search App</title>
    <script src="../static/comps.js" type="text/javascript"></script>
    <script src="../static/function.js"></script>
    <script type="text/javascript">
        fetchData(await fetch('comps.js'));
        let itsmomap, mark,
            lat = 35.6778614, lon = 139.7703167;

        function loaditsmomap() {
            itsmomap = new ZDC.itsmomap(
                document.getElementById('map'),
                {
                    latlon: new ZDC.LatLon(lat, lon),
                    zoom: 9,
                    itsmomapType: ZDC.MAPTYPE_HIGHRES_LV18
                }
            );
            const controllers = createElement('div');
            controllers.style.left= '10px';
            controllers.style.bottom= '10px';
            controllers.style.position= 'absolute';
            const scale = new ZDC.ScaleBar();
            scale.setPosition(ZDC.POSITION_BOTTOM_LEFT);

            const control = new ZDC.Control();
            control.setPosition(ZDC.POSITION_BOTTOM_RIGHT);
            
            controllers.addWidget(scale);
            controllers.addWidget(control);

            itsmomap.addWidget(controllers);

            /* 地図をクリックしたときの動作 */
            ZDC.addListener(itsmomap, ZDC.MAP_CLICK, makeMarker);
        };
        // let count = 1;
        function makeMarker() {
            // if (count > 30) return;
            /* クリックした地点にマーカを作成 */
            mark = new ZDC.Marker(MAP.getClickLatLon(),{
                color: ZDC.MARKER_COLOR_ID_BLUE_S,
                // number: ZDC['MARKER_NUMBER_ID_' + count + '_S']
            });
            // count++;
            /* マーカを追加 */
            itsmomap.addWidget(mark);
        };
    </script>
    <link rel="stylesheet" href="../static/style.css">
</head>
<body onload="loaditsmomap()">    
    <div id="map" >
        <div id="searchBox">
            <div id="searchbody">
                <input type="text" id="searchInput" placeholder="Type something..." autocomplete="off">
                <button id="searchButton">Search</button>
            </div>
            <div id="extras">
                <div id="suggestions" class="suggestions"></div>
                <hr>
                <div id="history" class="history">
                    <h3 style="text-align: center;">History</h3>
                    <ul id="historyList"></ul>
                </div>
            </div>
        </div>
        <div id="results">
            <button id="toggleButton" style="display: none;">▼ Show Results (0)</button>
            <div id="resultDetails"></div>
        </div>
    </div>
</body>
</html>