import mysql.connector as mySQL
import csv
import unicodedata

def changeType(cords) -> str:
    try:
        sign = 1 if cords[0] == '+' else -1
        parts = cords[1:].split(':')
        degrees = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        decimal = degrees + (minutes / 60) + (seconds / 3600)
        return sign * decimal
    except Exception as e:
        return False
def location(row) -> list:
    lon_decimal = changeType(row[-1])
    lat_decimal = changeType(row[-2])
    info = row[:-2]
    for i in range(len(info)):
        info[i] = unicodedata.normalize("NFKC", info[i])
    new_row = info + [lat_decimal, lon_decimal]
    return new_row
def main():
    file = "c://Users//8kesh//OneDrive//ドキュメント//Internship//Learning//data&api//mysql//miniT.tsv"
    myDB  = mySQL.connect(host='localhost', user='admin', password='password', database='testdb')
    myCursor = myDB.cursor()
    myCursor.execute("use testdb")
    myCursor.execute("DROP TABLE IF EXISTS test2")
    myCursor.execute("CREATE TABLE IF NOT EXISTS test2 (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255), place VARCHAR(255), altname VARCHAR(255), altplace VARCHAR(255), lat FLOAT, lon FLOAT)")
    with open(file, 'r', encoding='utf-8') as file:
        tsv_reader = csv.reader(file, delimiter='\t')
        insert_query6 = "INSERT INTO test2 (name, place, altname, altplace, lat, lon) VALUES (%s, %s, %s, %s, %s, %s)"
        insert_query4 = "INSERT INTO test2 (name, place, altname, altplace, lat, lon) VALUES (%s, NULL, %s, NULL, %s, %s)"
        for i, row in enumerate(tsv_reader, start=1):
            if i < 20000:
                row = list(row)
                correct_row = location(row)
                if False in correct_row:
                    pass
                else:
                    if len(correct_row) == 6:
                        myCursor.execute(insert_query6, correct_row)
                    else:
                        myCursor.execute(insert_query4, correct_row)
            else:
                break
    myCursor.execute("alter table test2 add fulltext(altname, altplace)")
    myDB.commit()
    print("20000 Data successfully imported from TSV to the database.")

if __name__ == "__main__":
    main()