import sqlite3
import csv

# Database setup
db_name = "japanese_data.db"
table_name = "locations"
tsv_file = "japanese_data.tsv"

# Step 1: Connect to the database
connection = sqlite3.connect(db_name)
cursor = connection.cursor()

# Step 2: Create a table
cursor.execute(f"""
CREATE TABLE IF NOT EXISTS {table_name} (
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- Auto-incrementing ID
    fullwidth_katakana TEXT,
    fullwidth_kanji TEXT,
    halfwidth_katakana_1 TEXT,
    halfwidth_katakana_2 TEXT,
    latitude REAL,
    longitude REAL
)
""")
connection.commit()

# Step 3: Read the TSV file and insert data
with open(tsv_file, 'r', encoding='utf-8') as file:
    tsv_reader = csv.reader(file, delimiter='\t')
    headers = next(tsv_reader)  # Read header row if present
    
    # Prepare the insert query
    insert_query = f"""
    INSERT INTO {table_name} 
    (fullwidth_katakana, fullwidth_kanji, halfwidth_katakana_1, halfwidth_katakana_2, latitude, longitude) 
    VALUES (?, ?, ?, ?, ?, ?)
    """
    
    for row in tsv_reader:
        # Insert the row into the table
        cursor.execute(insert_query, row)

connection.commit()

# Step 4: Verify the data
cursor.execute(f"SELECT * FROM {table_name}")
rows = cursor.fetchall()
for row in rows:
    print(row)

# Step 5: Close the database connection
connection.close()

print("Data successfully imported from TSV to the database.")
