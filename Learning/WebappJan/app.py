from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import mysql.connector as Mysql

from pykakasi import kakasi
kakasi = kakasi()
def convert_to_kana(text: str) -> str:
    kakasi.setMode("H", "K")  
    kakasi.setMode("J", "K")  
    conv = kakasi.getConverter()
    result = conv.do(text)
    return result

app = Flask(__name__)
CORS(app)  # Enable CORS for development purposes

@app.route('/')
def home():
    return render_template('index.html')  # Serves the HTML file

@app.route('/search', methods=['GET'])
def search():
    mquery = request.args.get('q', '').strip()  # Get and sanitize the query parameter
    kana_query = ''.join(convert_to_kana(char) for char in mquery)
    pipe_kana_query = "|".join(kana_query)
    wildcard_query = f"%{mquery}%"
    if not mquery:
        return jsonify([])  # Return empty list if query is missing

    try:
        # Establishing database connection
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="smallDB"
        )
        cursor = conn.cursor()
        cursor.execute("USE smalldb")  # Select the database to work with
        # Prepare SQL query with proper parameterization
        # sql = "SELECT * FROM fttest WHERE MATCH(name, place) AGAINST (%s IN NATURAL LANGUAGE MODE);"
        # wildcard_query = f"%{query}%"
        # sql = """
        #     SELECT * 
        #     FROM testTBl 
        #     WHERE 
        #         MATCH(ALTNAME, ALTPLACE) AGAINST (%s IN NATURAL LANGUAGE MODE) 
        #         AND (altname LIKE %s OR altplace LIKE %s);
        # """
        
        # cursor.execute(sql, (query, wildcard_query, wildcard_query))
        sql = "select * from testTbl where match(altname, altplace) against ('%s' in boolean mode) and (altname regexp '%s' or altplace regexp '%s') AND ((altname like '%s' and altplace like '%s') or (altname like '%s' and altplace like '%s');" 
        cursor.execute(sql, (pipe_kana_query, wildcard_query, wildcard_query, wildcard_query, wildcard_query, wildcard_query, wildcard_query))        
        # Fetch and process results
        results = cursor.fetchall()
        data = [
            {   "id" : row[0],
                "name": row[1],
                "place": row[2],
                "altname": row[3],
                "altplace": row[4],
                "lat": row[5],
                "lon": row[6]
            } for row in results
        ]
    except Mysql.Error as e:
        print(f"Error connecting to MySQL: {e}")  # Log error
        return jsonify({"error": "Database connection failed"}), 500  # Return error to client
    finally:
        # Safely close cursor and connection if initialized
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

    return jsonify(data)

@app.route('/saveHistory', methods=['POST'])
def save_history():
    data = request.get_json()
    name = data.get('name', '').strip()
    place = data.get('place', '').strip()
    altname = data.get('altname', '').strip()
    altplace = data.get('altplace', '').strip()
    lat = data.get('lat')
    lon = data.get('lon')
    if not name:
        return jsonify({"error": "Name is required"}), 400

    try:
        # Establishing database connection
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="historydb"
        )
        cursor = conn.cursor()
        cursor.execute("USE historydb")  # Select the database to work with
        # Insert the name into the history table
        sql = """
        INSERT IGNORE INTO historyTbl (name, place, altname, altplace, lat, lon) 
        VALUES (%s, %s, %s, %s, %s, %s);
        """
        cursor.execute(sql, (name, place, altname, altplace, lat, lon))
        conn.commit()
    except Mysql.Error as e:
        print(f"Error connecting to MySQL: {e}")  # Log error
        return jsonify({"error": "Database connection failed"}), 500  # Return error to client
    finally:
        # Safely close cursor and connection if initialized
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

    return jsonify({"message": "History saved successfully"})

@app.route('/getHistory', methods=['GET'])
def get_history():
    try:
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="historydb"
        )
        cursor = conn.cursor()
        cursor.execute("USE historydb")
        cursor.execute("SELECT * FROM historyTbl;")
        results = cursor.fetchall()

        data = [
            {
                "id": row[0],
                "name": row[1],
                "place": row[2],
                "altname": row[3],
                "altplace": row[4],
                "lat": row[5],
                "lon": row[6]
            } for row in results
        ]
    except Mysql.Error as e:
        print(f"Error: {e}")
        return jsonify({"error": "Failed to fetch history"}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

    return jsonify(data)

@app.route('/deleteHistory', methods=['DELETE'])
def delete_history():
    data = request.get_json()  # Receive the id to be deleted
    history_id = data.get('id')  # Get the id from the JSON payload

    if not history_id:
        return jsonify({"error": "ID is required"}), 400  # If no ID is provided, return error

    try:
        conn = Mysql.connect(
            host="localhost",
            user="admin",
            password="password",
            database="historydb"
        )
        cursor = conn.cursor()
        cursor.execute("USE historydb")  # Ensure we're working with the correct database
        
        # Execute delete query with parameterized query
        sql = "DELETE FROM historyTbl WHERE id = %s"
        cursor.execute(sql, (history_id,))  # Delete the record with the provided id
        conn.commit()

        if cursor.rowcount == 0:  # No rows deleted
            return jsonify({"error": "History item not found"}), 404  # If no item is found with this id
        return jsonify({"message": "History deleted successfully"})  # Success response

    except Mysql.Error as e:
        print(f"Error: {e}")
        return jsonify({"error": "Failed to delete history"}), 500  # Database error

    finally:
        if 'cursor' in locals():
            cursor.close()  # Close cursor to avoid any potential memory leaks
        if 'conn' in locals():
            conn.close()  # Close the connection

if __name__ == "__main__":
    app.run(debug=True)
