
    let marker; // Declare marker variable
    const resultDetails = document.getElementById('resultDetails');
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.getElementById('searchButton');
    const suggestionsBox = document.getElementById('suggestions');
    const toggleButton = document.getElementById('toggleButton');
    const historyBox = document.getElementById('history');
    const historyList = document.getElementById('historyList');
    let currentResults = [];

    // Limit for history items
    const MAX_HISTORY_ITEMS = 7;

    // Store history visibility state (initially hidden)
    let isHistoryBoxVisible = false;

    // Fetch suggestions from the server
    const fetchSuggestions = async (query) => {
        try {
        const response = await fetch(`/search?q=${query}`);
        if (!response.ok) {
            throw new Error('Failed to fetch suggestions');
        }
        const suggest = await response.json();
        suggestionsBox.innerHTML = suggest.map(
            s => `<div class="suggestion-item-info" data-lat="${s.lat}" data-lon="${s.lon}" data-name="${s.name}">${s.name} ${s.place}</div>`
        ).join('');
        suggestionsBox.style.display = 'block';

        // Add click event listener for each suggestion item
        document.querySelectorAll('.suggestion-item-info').forEach(item => {
            item.addEventListener('click', () => {
                const lat = item.getAttribute('data-lat');
                const lon = item.getAttribute('data-lon');
                const name = item.getAttribute('data-name');
                alert(`${name}: ${lat}, ${lon}`);
                saveHistory({ name, lat, lon, place: item.textContent });
            });
        });
        
        } catch (error) {
        console.error('Error fetching suggestions:', error);
        suggestionsBox.innerHTML = '<div class="error">Failed to fetch suggestions. Please try again.</div>';
        suggestionsBox.style.display = 'block';
        return [];
        }
    };
    // Fetch search results
    const fetchSearchResults = async (query) => {
        try {
            const response = await fetch(`/search?q=${query}`);
            const results = await response.json();
            return results;
        } catch (error) {
            console.error('Error fetching results:', error);
            return [];
        }
    };

    // Function to render history items from the server
    const renderHistory = async () => {
        try {
            const response = await fetch('/getHistory');
            if (!response.ok) throw new Error('Failed to fetch history');

            const historyItems = await response.json();

            // Limit the history to the maximum allowed items
            const limitedHistory = historyItems.reverse().slice(0, MAX_HISTORY_ITEMS);

            historyList.innerHTML = ''; // Clear the list
            limitedHistory.forEach(item => {
                const li = document.createElement('li');
                li.style.display = 'flex';
                li.style.justifyContent = 'space-between';
                li.style.width = '100%';
                const div = document.createElement('div');
                div.style.width = '90%';
                div.style.cursor = 'pointer';
                div.style.paddingLeft = '10px';
                div.style.paddingTop = '-10px';

                    const name = document.createElement('p');
                    name.textContent = item.name;
                    name.style.fontWeight = 'bold';
                    name.style.marginTop = '10px';
                    // name.style.marginBottom = '-10px';
                    name.style.fontSize = '16px';

                    const place = document.createElement('h5');
                    place.textContent = item.place;
                    place.style.color = '#666';
                    place.style.marginTop = '-10px';
                    place.style.marginBottom = '10px';
                    place.style.fontSize = '10px';

                const deleteBtn = document.createElement('span');
                deleteBtn.textContent = '✖';
                deleteBtn.style.cursor = 'pointer';
                deleteBtn.style.marginLeft = '10px';
                deleteBtn.style.color = '#ccc';
                deleteBtn.style.fontSize= '20px';
                deleteBtn.style.display = 'flex';
                deleteBtn.style.justifyContent = 'CENTER';
                deleteBtn.style.alignItems = 'CENTER';
                deleteBtn.style.position = 'relative';

                // Delete button click handler
                deleteBtn.addEventListener('click', (event) => {
                    event.stopPropagation(); // Prevent triggering the list item's click
                    deleteHistory(item.id);
                });

                li.addEventListener('click', () => {
                    showmapfromHistory(item);
                });
                div.appendChild(name);
                div.appendChild(place);
                li.appendChild(div);
                li.appendChild(deleteBtn);

                // Add the new item to the top
                historyList.prepend(li);
            });

            // Show or hide history box based on visibility
            historyBox.style.display = isHistoryBoxVisible ? 'block' : 'none';
        } catch (error) {
            console.error('Error rendering history:', error);
        }
    };


    // Function to save a history item without duplicates
    const saveHistory = async (result) => {
        try {
            const response = await fetch('/saveHistory', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: result.name,
                    place: result.place,
                    altname: result.altname,
                    altplace: result.altplace,
                    lat: result.lat,
                    lon: result.lon
                })
            });

            if (!response.ok) throw new Error('Failed to save history');

            const data = await response.json();
            console.log('History saved:', data);

            // Fetch updated history to render
            renderHistory();
        } catch (error) {
            console.error('Error saving history:', error);
        }
    };


    // Function to delete a history item based on its ID
    const deleteHistory = (id) => {
        fetch('/deleteHistory', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id: id })
        }).then(response => {
            if (response.ok) {
                renderHistory(); // Re-render after deletion
                isHistoryBoxVisible = true; // Keep the history box visible after deletion
            }
        });
    };

    // Function to show history item details
    const showmapfromHistory = (item) => {
        deleteHistory(item.id); // Save the history when clicked
        saveHistory(item);
        // pinpointLocation(item.lat, item.lon, item.name);
        alert(`${item.name}: ${item.lat}, ${item.lon}`);
    };

    // Handle Click for history box
    searchInput.addEventListener('click', () => {
        isHistoryBoxVisible = true; // Show history box when input is clicked
        historyBox.style.display = 'block'; 
    });
    // Handle input for suggestions
    searchInput.addEventListener('input', async () => {
        const query = searchInput.value;
        if (query.length > 0) {
            const suggestions = await fetchSuggestions(query);
            if (suggestions.length > 0) {
                suggestionsBox.style.display = 'block';
                suggestionsBox.innerHTML = suggestions.map(s => `<div class="suggestion-item">${s.name}</div>`).join('');
            } else {
                suggestionsBox.style.display = 'none';
            }
        } else {
            suggestionsBox.style.display = 'none';
        }
    });

    // Handle search button click
    searchButton.addEventListener('click', async () => {
        const query = searchInput.value;
        const results = await fetchSearchResults(query);
        resultData(results);
        suggestionsBox.style.display = 'none';
        historyBox.style.display = 'none'; // Hide history box after search
    });

    // Function to display results on the page
    const resultData = (results) => {
        currentResults = results;
        resultDetails.innerHTML = '';
        toggleButton.style.display = results.length > 0 ? 'block' : 'none';
        if (results.length > 0) {
            toggleButton.textContent = `▲ Hide Results (${results.length})`;
            results.forEach(result => {
                const container = document.createElement('div');
                container.style.border = '#ccc';
                container.style.borderRadius = '5px';
                container.style.marginTop = '-5px';
                // Correct use of setProperty to define a CSS variable
                container.style.setProperty("--div-background-color", "#f9f9f9");
                // Apply the variable in backgroundColor
                container.style.backgroundColor = "var(--div-background-color)";
                const detailDiv = document.createElement('div');
                detailDiv.className = 'detailDiv';
                detailDiv.style.width = '100%';
                detailDiv.style.cursor = 'pointer';
                detailDiv.style.paddingLeft = '10px';

                

                const name = document.createElement('div');
                name.textContent = result.name;
                name.style.fontWeight = 'bold';
                name.style.marginTop = '10px';
                name.style.overflow = 'hidden';

                const place = document.createElement('div');
                place.textContent = result.place;
                place.style.color = '#666';
                place.style.fontSize = '12px';

                const latLon = document.createElement('div');
                latLon.textContent = `${result.lat}, ${result.lon}`;
                latLon.style.color = '#666';
                latLon.style.fontSize = '12px';

                // Append details to the detailDiv
                detailDiv.appendChild(name);
                detailDiv.appendChild(place);
                detailDiv.appendChild(latLon);

                // Append detailDiv to the container
                container.appendChild(detailDiv);

                // Append the container to resultDetails
                resultDetails.appendChild(container);


                detailDiv.addEventListener('click', () => {
                    saveHistory(result); // Save result to history when clicked
                    alert(`${result.name}: ${result.lat}, ${result.lon}`);
                    // pinpointLocation(result.lat, result.lon, result.name);
                });
            });
            resultDetails.style.display = 'block';
        } else {
            resultDetails.style.display = 'none';
        }
    };

    // Handle toggle button click
    toggleButton.addEventListener('click', () => {
        if (resultDetails.style.display === 'none' || resultDetails.style.display === '') {
            resultDetails.style.display = 'block';
            toggleButton.textContent = `▲ Hide Results (${currentResults.length})`;
        } else {
            resultDetails.style.display = 'none';
            toggleButton.textContent = `▼ Show Results (${currentResults.length})`;
        }
    });

    // Function to pinpoint location on the map
    function pinpointLocation(lat, lon, name) {
        if (!marker) {
            marker = L.marker([lat, lon]).addTo(map);
        } else {
            marker.setLatLng([lat, lon]);
        }
        map.setView([lat, lon], 13);
        alert(`${name}: ${lat}, ${lon}`);
    }

    // Hide history box when input loses focus
    searchInput.addEventListener('blur', () => {
        setTimeout(() => { // Using setTimeout to prevent immediate hide
            if (!isHistoryBoxVisible) {
                historyBox.style.display = 'none'; 
            }
        }, 200);  // Delay to allow click event propagation
    });

    document.addEventListener('click', (event) => {
        // Check if the click happened outside the search input or history box
        const isClickInsideInput = searchInput.contains(event.target);
        const isClickInsideHistory = historyBox.contains(event.target);

        if (!isClickInsideInput && !isClickInsideHistory) {
            // Hide the history box
            historyBox.style.display = 'none';
            suggestionsBox.style.display = 'none';
            isHistoryBoxVisible = false;

        }
    });


    // Initial render for history
    renderHistory();
