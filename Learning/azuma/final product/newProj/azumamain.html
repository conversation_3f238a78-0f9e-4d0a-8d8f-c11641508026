<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>あずまホームページ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="static/styles.css">
    <style>
    * {
        margin: 0;
        padding: 0;
        font-family: 'Courier New', monospace;
        box-sizing: border-box;
    }
    table {
        width: 80%;
        border-collapse: collapse;
        margin: 0 auto;
    }
    th, td {
        border: 1px solid #fff;
        padding: 10px;
        text-align: center;
    }
    th {
        background-color: #93af4c;
        color: #fff;
    }
    td {
        background-color: #31d580c4;
        color: #fff;
    }
    iframe {
        width: 650px;
        height: 450px;
        margin: 0 auto;
        display: block;
        height: 300px;

    }
    </style>
</head>
<body>
    <header id="home">
        <button id="menuButton" onclick="toggleNav()"><i class="fas fa-bars" style="font-size: 24px; color: white;"></i></button>
        <div>
            <img src="../azuma_logo.png" alt="Azuma Logo">
        </div>
    </header>

    <div class="sidenav" id="mySidenav">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <a href="azumamain.html">ホーム</a>
        <a href="classroom.html">授業</a>
        <a href="Schedule.html">スケジュール</a>
        <a href="download.html">ダウンロード</a>
        <a href="consultation.html">相談</a>
        <a href="servicelists.html">サービス</a>
    </div>
    <nav class="navbar">
        <a href="#home">ホーム</a>
        <a href="#main">メイン</a>
        <a href="#contents">コンテンツ</a>
        <a href="#contact">コンタクト</a>
        <a href="download.html">ダウンロード</a>
    </nav>
    <div id="language-options">
        <div id="google_translate_element"></div>
        <script>
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: 'jp',
                    includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                }, 'google_translate_element');
            }
        </script>
        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
    </div>
    <hr>
        <div id="news" style="margin-bottom: 20px;">
            <h2>更新情報</h2>
            <div id="updates">
            <table>
                <tr>
                <th>日付</th>
                <th>情報</th>
                </tr>
                <tr>
                <td>11/8</td>
                <td>背景を変更しました</td>
                </tr>
                <tr>
                <td>11/8</td>
                <td>画像をダウンロードしました</td>
                </tr>
                <tr>
                <td>11/8</td>
                <td>ヘッダーとフッターのテンプレート</td>
                </tr>
                <tr>
                <td>11/8</td>
                <td>プロジェクトを開始しました</td>
                </tr>
            </table>
            </div>
        </div>
        <hr>
        <main>
            <section id="main">
            <div id="language-options">
                <div id="google_translate_element">
                </div>
                <script>
                function googleTranslateElementInit() {
                    new google.translate.TranslateElement({
                    pageLanguage: 'jp',
                    includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                    }, 'google_translate_element');
                }
                </script>
                <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
            </div>
            <div class="about-the-course" id="bodyimg">
                <h4>コースについて</h4>
                <img src="course.jpg" alt="" style="width: 80%; height: auto;">
                <img src="business-application.jfif" alt="">
                <p>ビジネスアプリケーション</p>
                <p>IT業界の経験豊富な講師が、より実践的な学習指導を行います。学生は、スプレッドシート、文書管理、文書作成などのビジネスで使用されるソフトウェアや、ATMやオークションサイトなど特定のタスクのために開発されたコンピュータシステムについて学びます。学生は、ExcelやWordの実践、Web開発のためのHTML、AI開発に使用されるプログラミング言語の1つであるPythonを学びます。</p>
            </div>
            <div class="career-prospective" id="bodyimg">
                <h4>キャリアの展望</h4>
                <p>就職に関しては、毎年卒業する学生の約95％が最終学年の最初の学期に選ばれていることをお知らせします。</p>
                <p>私たちの学生は、世界のトップ企業のいくつかに就職し、業界で名を馳せることができました。</p>
                <img src="placement.png" alt="">
            </div>
            <div class="message-from-the-principal" id="bodyimg">
                <h4>校長からのメッセージ</h4>
                <img src="principal.jfif" alt="">
                <div class="message-box">
                <p class="message-here">校長として、これから入学する皆さんを心から歓迎します。</p>
                </div>
            </div>
            <div class="appealing-aspects" id="bodyimg">
                <h4>魅力的な側面</h4>
                <p>私たちの学校の環境は、その魅力的な側面であり、世界中から学生を引き付けるのに役立っています。</p>
                <p>私たちは、特定の分野で仕事を得るために必要な知識を十分に提供しており、将来のトラブルを避けることができます。</p>
            </div>
            <div id="access">
                <h4>アクセス</h4>
                <p>私たちの学校は、神奈川県横浜市の中心部に位置しています。学校は公共交通機関で簡単にアクセスでき、主要な場所に位置しています。</p>
                <br>
                <div class="map"><iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d13001.997779200568!2d139.61632579565048!3d35.44242785145109!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60185c8bde9965bd%3A0xef7223fb70cb6319!2z44GC44Ga44G-5bel56eR5bCC6ZaA5a2m5qCh!5e0!3m2!1sen!2sjp!4v1733452408370!5m2!1sen!2sjp" width="400" height="300" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe></div>
            </div>
            </section>
        </main>
        <hr>
    <footer>
        <section>
            <h3 style="border-bottom: #ffffff inset 2px;">Information</h3>
            <br>
            <a href="#home"><img src="../azuma_logo.png" alt=""></a>
            <div id="contact">
                <address style="font-size: medium; font-style: normal; color: #fff;">
                    <div><strong>神奈川県横浜市中区初音町1-2-2</strong></div>
                    <div>Tel: ************</div>
                    <div>Fax: ************</div>
                    <div>Mail: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                    <div>（平日 9:00 ~ 17:00）</div>
                </address>
            </div>
            <br>
            <div id="contents">
                <h3 style="border-bottom: #ffffff inset 2px;">Contents</h3>
                <br>
                <nav>
                    <a href="OpenCampus.html">オーペンキャンパス</a><br>
                    <a href="admission.html">入学案内</a><br>
                    <a href="schooltour.html">学校ツアー</a><br>
                    <a href="placement.html">就職歴</a><br>
                </nav>
            </div>
            <br>
            <div>
                <p style="text-align: right; font-size: small">&copy; 2025 あずま工科専門学校</p>
            </div>
        </section>
    </footer>
    <button id="goToTop" onclick="scrollToTop()"><i class="fas fa-arrow-up"></i></button>
    <script>
        
        const menuButton = document.getElementById("menuButton");
        const sideNav = document.getElementById("mySidenav");

        function toggleNav() {
            sideNav.classList.toggle("open");
        }

        function closeNav() {
            sideNav.classList.remove("open");
        }

        document.addEventListener('click', function (event) {
            if (!sideNav.contains(event.target) && !menuButton.contains(event.target) && sideNav.classList.contains('open')) {
                closeNav();
            }
        });

        window.onscroll = function () {
            const goToTop = document.getElementById("goToTop");
            if (document.documentElement.scrollTop > 100) {
                goToTop.classList.add("show");
            } else {
                goToTop.classList.remove("show");
            }
        };

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>