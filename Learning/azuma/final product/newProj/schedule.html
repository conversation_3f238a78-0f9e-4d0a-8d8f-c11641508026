<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>あずまホームページ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="static/styles.css">
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        th {
            background-color: #f4f4f4;
        }
        @media screen and (max-width: 600px) {
            table {
            width: 100%;
            display: block;
            overflow-x: auto;
            white-space: nowrap;
            }

            th, td {
            display: inline-block;
            width: auto;
            padding: 4px;
            text-align: left;
            }

            th {
            background-color: #0073e6;
            color: white;
            }
        }
    </style>
    </head>
    <body>
        <header id="home">
            <button id="menuButton" onclick="toggleNav()"><i class="fas fa-bars" style="font-size: 24px; color: white;"></i></button>
            <div>
                <img src="../azuma_logo.png" alt="Azuma Logo">
            </div>
        </header>
    
        <div class="sidenav" id="mySidenav">
            <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
            <a href="azumamain.html">ホーム</a>
            <a href="classroom.html">授業</a>
            <a href="Schedule.html">スケジュール</a>
            <a href="download.html">ダウンロード</a>
            <a href="consultation.html">相談</a>
            <a href="servicelists.html">サービス</a>
        </div>
        <nav class="navbar">
            <a href="#home">ホーム</a>
            <a href="#main">メイン</a>
            <a href="#contents">コンテンツ</a>
            <a href="#contact">コンタクト</a>
            <a href="download.html">ダウンロード</a>
        </nav>
        <div id="language-options">
            <div id="google_translate_element"></div>
            <script>
                function googleTranslateElementInit() {
                    new google.translate.TranslateElement({
                        pageLanguage: 'jp',
                        includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                        layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                    }, 'google_translate_element');
                }
            </script>
            <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
        </div>
        <main>
            <section id="main">
                    <h2>スケジュール</h2>
                    <p>
                        各学期の授業日程、イベント、試験日程などは、定期的に更新されています。<br>
                        詳細なスケジュールは学校からのお知らせをご確認ください。
                    </p>
                    <h3>スケジュール</h3>
                    <ul id="scheduleTable">
                    </ul>
                    <br>
                    <div id="schedule-container"></div>
                    <h3>イベント</h3>
                    <p>
                        学校祭やスポーツ大会など、年間を通じて様々なイベントが開催されます。<br>
                        学生同士の交流を深める良い機会です。
                    </p>
                    <h3>年間スケジュール</h3>
                    <hr>
                    <img src="images/schedule.jpg" alt="スケジュール" style="width: 80%;">
            </section>
        </main>
        <footer>
            <section>
                <h3 style="border-bottom: #ffffff inset 2px;">Information</h3>
                <br>
                <a href="#home"><img src="../azuma_logo.png" alt=""></a>
                <div id="contact">
                    <address style="font-size: medium; font-style: normal; color: #fff;">
                        <div><strong>神奈川県横浜市中区初音町1-2-2</strong></div>
                        <div>Tel: ************</div>
                        <div>Fax: ************</div>
                        <div>Mail: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                        <div>（平日 9:00 ~ 17:00）
                    </address>
                </div>
                <br>
                <div id="contents">
                    <h3 style="border-bottom: #ffffff inset 2px;">Contents</h3>
                    <br>
                    <nav>
                        <a href="OpenCampus.html">オーペンキャンパス</a><br>
                        <a href="admission.html">入学案内</a><br>
                        <a href="schooltour.html">学校ツアー</a><br>
                        <a href="placement.html">就職歴</a><br>
                    </nav>
                </div>
                <br>
                <div>
                    <p style="text-align: right; font-size: small">&copy; 2025 あずま工科専門学校</p>
                </div>
            </section>
        </footer>
        <button id="goToTop" onclick="scrollToTop()"><i class="fas fa-arrow-up"></i></button>
        <script>
            
            const menuButton = document.getElementById("menuButton");
            const sideNav = document.getElementById("mySidenav");
    
            function toggleNav() {
                sideNav.classList.toggle("open");
            }
    
            function closeNav() {
                sideNav.classList.remove("open");
            }
    
            document.addEventListener('click', function (event) {
                if (!sideNav.contains(event.target) && !menuButton.contains(event.target) && sideNav.classList.contains('open')) {
                    closeNav();
                }
            });
    
            window.onscroll = function () {
                const goToTop = document.getElementById("goToTop");
                if (document.documentElement.scrollTop > 100) {
                    goToTop.classList.add("show");
                } else {
                    goToTop.classList.remove("show");
                }
            };
    
            function scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
    
            const scheduleTable = document.getElementById('scheduleTable');
            scheduleTable.style.width = '75%';
            scheduleTable.style.margin = 'auto';
    
            const days = ['日', '月', '火', '水', '木', '金', '土'];
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth();
            const date = today.getDate();
            const day = today.getDay();
    
            const firstDay = new Date(year, month, 1).getDay();
            const lastDate = new Date(year, month + 1, 0).getDate();
    
            const calendar = document.createElement('table');
            calendar.style.width = '100%';
            calendar.style.borderCollapse = 'collapse';
    
            const headerRow = document.createElement('tr');
            days.forEach(day => {
                const th = document.createElement('th');
                th.textContent = day;
                th.style.border = '1px solid #ddd';
                th.style.padding = '8px';
                th.style.backgroundColor = '#c9c9c9';
                headerRow.appendChild(th);
            });
            calendar.appendChild(headerRow);
    
            let row = document.createElement('tr');
            for (let i = 0; i < firstDay; i++) {
                const cell = document.createElement('td');
                cell.style.border = '1px solid #ddd';
                cell.style.padding = '4px';
                row.appendChild(cell);
            }
    
            for (let i = 1; i <= lastDate; i++) {
                if ((firstDay + i - 1) % 7 === 0) {
                    calendar.appendChild(row);
                    row = document.createElement('tr');
                }
                const cell = document.createElement('td');
                cell.addEventListener('mouseover', () => {
                    cell.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.1)';
                });
                cell.addEventListener('mouseout', () => {
                    cell.style.boxShadow = 'none';
                });
                cell.addEventListener('click', () => {
                    presentSchedule(i);
                });
                const dateDiv = document.createElement('div');
                dateDiv.textContent = i;
                cell.appendChild(dateDiv);
                cell.style.cursor = 'pointer';
                cell.style.border = '1px solid #ddd';
                cell.style.padding = '18px';
                if (i === date) {
                    cell.style.backgroundColor = '#0073e6';
                    cell.style.color = '#fff';
                }
                if ((firstDay + i - 1) % 7 === 0 || (firstDay + i - 1) % 7 === 6) {
                    cell.style.color = 'red';
                }
                row.appendChild(cell);
            }
            calendar.appendChild(row);
    
            scheduleTable.appendChild(calendar);
    
            // Updated PresentSchedule function
            function presentSchedule(selectedDate) {
                const TodaysSchedule = document.createElement('table');
                TodaysSchedule.style.width = '100%';
                TodaysSchedule.style.borderCollapse = 'collapse';
                const headerRowSchedule = document.createElement('tr');
                const headerSchedule1 = document.createElement('th');
                headerSchedule1.textContent = year + '年' + (month + 1) + '月' + selectedDate + '日' + '(' + days[(new Date(year, month, selectedDate)).getDay()] + ')';
                headerSchedule1.style.border = '1px solid #ddd';
                headerSchedule1.style.padding = '8px';
                headerSchedule1.style.backgroundColor = '#c9c9c9';
                headerRowSchedule.appendChild(headerSchedule1);
    
                const headerSchedule2 = document.createElement('th');
                headerSchedule2.textContent = '授業';
                headerSchedule2.style.border = '1px solid #ddd';
                headerSchedule2.style.padding = '8px';
                headerSchedule2.style.backgroundColor = '#c9c9c9';
                headerRowSchedule.appendChild(headerSchedule2);
    
                TodaysSchedule.appendChild(headerRowSchedule);
    
    
                // Schedule times and subjects
                const SchoolTimes = [
                    ['9:00~9:45', '9:55~10:40', '10:50~11:35', '11:45~12:30'],
                    ['13:20~14:05', '14:15~15:00', '15:10~15:55', '16:05~16:50']
                ];
    
                // Subjects mapped to the time slots for each weekday
                const subjectsByDay = {
                    0: ['休み', '休み', '休み', '休み', '休み', '休み', '休み', '休み'], // Sunday
                    1: ['Python', 'Python', 'Html', 'Html', 'AI', 'AI', '総合日本語', '総合日本語'], // Monday
                    2: ['Excel', 'Excel', 'Fusion', 'Fusion', 'Word', 'Word', 'キャリア', 'キャリア'], // Tuesday
                    3: ['ビジネス日本語', 'ビジネス日本語', '卒業制作', '卒業制作', 'Python', 'Python', 'Html', 'Html'], // Wednesday
                    4: ['AI', 'AI', '総合日本語', '総合日本語', 'Excel', 'Excel', 'Fusion', 'Fusion'], // Thursday
                    5: ['Word', 'Word', 'キャリア', 'キャリア', 'ビジネス日本語', 'ビジネス日本語', '卒業制作', '卒業制作'], // Friday
                    6: ['休み', '休み', '休み', '休み', '休み', '休み', '休み', '休み'] // Saturday
                };
    
                const selectedDay = new Date(year, month, selectedDate).getDay();
                const subjects = subjectsByDay[selectedDay];
    
                // Create rows for each time slot and display the corresponding subject
                for (let i = 0; i < SchoolTimes[0].length; i++) {
                    const scheduleRow = document.createElement('tr');
                    const timeCell = document.createElement('td');
                    timeCell.textContent = SchoolTimes[0][i];
                    timeCell.style.border = '1px solid #ddd';
                    timeCell.style.padding = '8px';
                    scheduleRow.appendChild(timeCell);
    
                    const subjectCell = document.createElement('td');
                    subjectCell.textContent = subjects[i]; // Get the subject for the slot
                    subjectCell.style.border = '1px solid #ddd';
                    subjectCell.style.padding = '8px';
                    scheduleRow.appendChild(subjectCell);
    
                    TodaysSchedule.appendChild(scheduleRow);
                }
    
                // Add the schedule to the page
                const scheduleContainer = document.getElementById('schedule-container');
                scheduleContainer.innerHTML = ''; // Clear previous content
                scheduleContainer.appendChild(TodaysSchedule);
            }
            const classroomFacilities = document.getElementById('classroomFacilities');
            const facilities = ['Python', 'Html', 'AI', '総合日本語', 'Excel', 'Fusion', 'Word', 'キャリア', 'ビジネス日本語', '卒業制作'];
            const classroomList = document.getElementById('classroomList');
            const classroom = ["AP2-1", "AP2-2", "AP1-1", "AP1-2", "AP1-3", "AP1-4", "AP1-5"];
    
            const classroomTable = document.createElement('table');
            classroomTable.style.width = '100%';
            classroomTable.style.borderCollapse = 'collapse';
                const headerRowClassroom = document.createElement('tr');
                const headerClassroom1 = document.createElement('th');
                headerClassroom1.textContent = '教室';
                headerClassroom1.style.border = '1px solid #ddd';
                headerClassroom1.style.padding = '8px';
                headerClassroom1.style.backgroundColor = '#f4f4f4';
                headerRowClassroom.appendChild(headerClassroom1);
    
                const headerClassroom2 = document.createElement('th');
                headerClassroom2.textContent = '設備';
                headerClassroom2.style.border = '1px solid #ddd';
                headerClassroom2.style.padding = '8px';
                headerClassroom2.style.backgroundColor = '#f4f4f4';
                headerRowClassroom.appendChild(headerClassroom2);
    
                classroomTable.appendChild(headerRowClassroom);
    
                for (let i = 0; i < classroom.length; i++) {
                    const row = document.createElement('tr');
                    const cellClassroom = document.createElement('td');
                    cellClassroom.textContent = classroom[i];
                    cellClassroom.style.border = '1px solid #ddd';
                    cellClassroom.style.padding = '8px';
                    row.appendChild(cellClassroom);
    
                    const cellFacility = document.createElement('td');
                    cellFacility.textContent = facilities[i % facilities.length];
                    cellFacility.style.border = '1px solid #ddd';
                    cellFacility.style.padding = '8px';
                    row.appendChild(cellFacility);
    
                    classroomTable.appendChild(row);
                }
    
                classroomFacilities.appendChild(classroomTable);
        </script>
    </body>
    </html>
        