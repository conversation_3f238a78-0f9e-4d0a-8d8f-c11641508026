<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>あずまホームページ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="static/styles.css">
</head>
<body>
    <header id="home">
        <button id="menuButton" onclick="toggleNav()"><i class="fas fa-bars" style="font-size: 24px; color: white;"></i></button>
        <div>
            <img src="../azuma_logo.png" alt="Azuma Logo">
        </div>
    </header>

    <div class="sidenav" id="mySidenav">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <a href="azumamain.html">ホーム</a>
        <a href="classroom.html">授業</a>
        <a href="Schedule.html">スケジュール</a>
        <a href="download.html">ダウンロード</a>
        <a href="consultation.html">相談</a>
        <a href="servicelists.html">サービス</a>
    </div>
    <nav class="navbar">
        <a href="#home">ホーム</a>
        <a href="#main">メイン</a>
        <a href="#contents">コンテンツ</a>
        <a href="#contact">コンタクト</a>
        <a href="download.html">ダウンロード</a>
    </nav>
    <div id="language-options">
        <div id="google_translate_element" style="height: 30px;"></div>
        <script>
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: 'jp',
                    includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                }, 'google_translate_element');
            }
        </script>
        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
    </div>
    <main>
        <section id="main">  
            <h2>教室</h2>
            <p>
                最新のICT設備を備えた教室で、実践的な学習環境が整っています。<br>
                少人数制のクラスで、丁寧な指導と充実したカリキュラムを実現しています。
            </p>
            <h3>教室一覧</h3>
            <ul id="classroomList">
                <!-- Classroom list items will be added here by JavaScript -->
            </ul>
            <h3>設備</h3>
            <ul id="classroomFacilities">
                <!-- Classroom facilities will be added here by JavaScript -->
            </ul>
            <h3>学習環境</h3>
            <p>
                少人数制のクラスで、学生一人ひとりに目が届くよう配慮しています。<br>
                教員との距離が近く、質問や相談がしやすい環境です。
            </p>
            <img src="images/azumaImg.jpg" alt="教室" style="width: 80%;">
        </section>
    </main>
    <footer>
        <section>
            <h3 style="border-bottom: #ffffff inset 2px;">Information</h3>
            <br>
            <a href="#home"><img src="../azuma_logo.png" alt=""></a>
            <div id="contact">
                <address style="font-size: medium; font-style: normal; color: #fff;">
                    <div><strong>神奈川県横浜市中区初音町1-2-2</strong></div>
                    <div>Tel: ************</div>
                    <div>Fax: ************</div>
                    <div>Mail: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                    <div>（平日 9:00 ~ 17:00）</div>
                </address>
            </div>
            <br>
            <div id="contents">
                <h3 style="border-bottom: #ffffff inset 2px;">Contents</h3>
                <br>
                <nav>
                    <a href="OpenCampus.html">オーペンキャンパス</a><br>
                    <a href="admission.html">入学案内</a><br>
                    <a href="schooltour.html">学校ツアー</a><br>
                    <a href="placement.html">就職歴</a><br>
                </nav>
            </div>
            <br>
            <div>
                <p style="text-align: right; font-size: small">&copy; 2025 あずま工科専門学校</p>
            </div>
        </section>
    </footer>
    <button id="goToTop" onclick="scrollToTop()"><i class="fas fa-arrow-up"></i></button>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const classroomFacilities = document.getElementById('classroomFacilities');
            const facilities = ['Python', 'Html', 'AI', '総合日本語', 'Excel', 'Fusion', 'Word', 'キャリア', 'ビジネス日本語', '卒業制作'];
            const classroomList = document.getElementById('classroomList');
            const classroom = ["AP2-1", "AP2-2", "AP1-1", "AP1-2", "AP1-3", "AP1-4", "AP1-5"];

            const classroomTable = document.createElement('table');
            classroomTable.style.width = '100%';
            classroomTable.style.borderCollapse = 'collapse';
            const headerRowClassroom = document.createElement('tr');
            const headerClassroom1 = document.createElement('th');
            headerClassroom1.textContent = '教室';
            headerClassroom1.style.border = '1px solid #ddd';
            headerClassroom1.style.padding = '8px';
            headerClassroom1.style.backgroundColor = '#c9c9c9';
            headerRowClassroom.appendChild(headerClassroom1);

            const headerClassroom2 = document.createElement('th');
            headerClassroom2.textContent = '設備';
            headerClassroom2.style.border = '1px solid #ddd';
            headerClassroom2.style.padding = '8px';
            headerClassroom2.style.backgroundColor = '#c9c9c9';
            headerRowClassroom.appendChild(headerClassroom2);

            classroomTable.appendChild(headerRowClassroom);

            for (let i = 0; i < classroom.length; i++) {
                const row = document.createElement('tr');
                const cellClassroom = document.createElement('td');
                cellClassroom.textContent = classroom[i];
                cellClassroom.style.border = '1px solid #ddd';
                cellClassroom.style.padding = '8px';
                row.appendChild(cellClassroom);

                const cellFacility = document.createElement('td');
                cellFacility.textContent = facilities[i % facilities.length];
                cellFacility.style.border = '1px solid #ddd';
                cellFacility.style.padding = '8px';
                row.appendChild(cellFacility);

                classroomTable.appendChild(row);
            }

            classroomFacilities.appendChild(classroomTable);
        });

        const menuButton = document.getElementById("menuButton");
        const sideNav = document.getElementById("mySidenav");

        function toggleNav() {
            sideNav.classList.toggle("open");
        }

        function closeNav() {
            sideNav.classList.remove("open");
        }

        document.addEventListener('click', function (event) {
            if (!sideNav.contains(event.target) && !menuButton.contains(event.target) && sideNav.classList.contains('open')) {
                closeNav();
            }
        });

        window.onscroll = function () {
            const goToTop = document.getElementById("goToTop");
            if (document.documentElement.scrollTop > 100) {
                goToTop.classList.add("show");
            } else {
                goToTop.classList.remove("show");
            }
        };

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
