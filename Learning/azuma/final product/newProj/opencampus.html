<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>あずまホームページ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="static/styles.css">

</head>
<body>
    <header id="home">
        <button id="menuButton" onclick="toggleNav()"><i class="fas fa-bars" style="font-size: 24px; color: white;"></i></button>
        <div>
            <img src="../azuma_logo.png" alt="Azuma Logo">
        </div>
    </header>

    <div class="sidenav" id="mySidenav">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <a href="azumamain.html">ホーム</a>
        <a href="classroom.html">授業</a>
        <a href="Schedule.html">スケジュール</a>
        <a href="download.html">download</a>
        <a href="consultation.html">相談</a>
        <a href="servicelists.html">サービス</a>
    </div>
    <nav class="navbar">
        <a href="#home">ホーム</a>
        <a href="#main">メイン</a>
        <a href="#contents">コンテンツ</a>
        <a href="#contact">コンタクト</a>
        <a href="download.html">ダウンロード</a>
    </nav>
    <div id="language-options">
        <div id="google_translate_element"></div>
        <script>
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: 'jp',
                    includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                }, 'google_translate_element');
            }
        </script>
        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
    </div>
    <main>
        <section id="main">
                <h2>オープンキャンパス</h2>
                <h3>個別オープンキャンパス</h3>
                <p>好きな日時に予約して、個別相談が可能です。授業内容、学費、サポート体制について詳しく聞くことができます。</p>
                <p>所要時間は約1時間です。</p>
        
                <h3>来校型オープンキャンパス</h3>
                <ol>
                    <li>ご予約：電話または申込フォームから予約してください。</li>
                    <li>受付：1階入り口でスタッフが案内します。</li>
                    <li>学校説明：質問を自由にして、入学後のイメージを深めましょう。</li>
                    <li>校内見学：授業風景を実際に見ることができます。</li>
                    <li>フリータイム：出願、学費、住まいのことなど、自由に相談できます。</li>
                    <li>終了：質問があればいつでもご連絡ください。</li>
                </ol>
        
                <h3>オンラインオープンキャンパス</h3>
                <p>Zoomを使用して、遠方にお住まいの方や来校が難しい方も参加可能です。</p>
                <p>参加後、担当者からメールで連絡があります。</p>
                <img src="images/opencampus.jpg" alt="オープンキャンパス" style="width: 80%;">

        </section>
    </main>
    <footer>
        <section>
            <h3 style="border-bottom: #ffffff inset 2px;">Information</h3>
            <br>
            <a href="#home"><img src="../azuma_logo.png" alt=""></a>
            <div id="contact">
                <address style="font-size: medium; font-style: normal; color: #fff;">
                    <div><strong>神奈川県横浜市中区初音町1-2-2</strong></div>
                    <div>Tel: ************</div>
                    <div>Fax: ************</div>
                    <div>Mail: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                    <div>（平日 9:00 ~ 17:00）</div>
                </address>
            </div>
            <br>
            <div id="contents">
                <h3 style="border-bottom: #ffffff inset 2px;">Contents</h3>
                <br>
                <nav>
                    <a href="OpenCampus.html">オーペンキャンパス</a><br>
                    <a href="admission.html">入学案内</a><br>
                    <a href="schooltour.html">学校ツアー</a><br>
                    <a href="placement.html">就職歴</a><br>
                </nav>
            </div>
            <br>
            <div>
                <p style="text-align: right; font-size: small">&copy; 2025 あずま工科専門学校</p>
            </div>
        </section>
    </footer>
    <button id="goToTop" onclick="scrollToTop()"><i class="fas fa-arrow-up"></i></button>
    <script>
        
        const menuButton = document.getElementById("menuButton");
        const sideNav = document.getElementById("mySidenav");

        function toggleNav() {
            sideNav.classList.toggle("open");
        }

        function closeNav() {
            sideNav.classList.remove("open");
        }

        document.addEventListener('click', function (event) {
            if (!sideNav.contains(event.target) && !menuButton.contains(event.target) && sideNav.classList.contains('open')) {
                closeNav();
            }
        });

        window.onscroll = function () {
            const goToTop = document.getElementById("goToTop");
            if (document.documentElement.scrollTop > 100) {
                goToTop.classList.add("show");
            } else {
                goToTop.classList.remove("show");
            }
        };

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>