const scheduleTable = document.getElementById('scheduleTable');

const days = ['日', '月', '火', '水', '木', '金', '土'];
const today = new Date();
const year = today.getFullYear();
const month = today.getMonth();
const date = today.getDate();
const day = today.getDay();

const firstDay = new Date(year, month, 1).getDay();
const lastDate = new Date(year, month + 1, 0).getDate();

const calendar = document.createElement('table');
calendar.style.width = '100%';
calendar.style.borderCollapse = 'collapse';

const headerRow = document.createElement('tr');
days.forEach(day => {
    const th = document.createElement('th');
    th.textContent = day;
    th.style.border = '1px solid #ddd';
    th.style.padding = '8px';
    th.style.backgroundColor = '#f4f4f4';
    headerRow.appendChild(th);
});
calendar.appendChild(headerRow);

let row = document.createElement('tr');
for (let i = 0; i < firstDay; i++) {
    const cell = document.createElement('td');
    cell.style.border = '1px solid #ddd';
    cell.style.padding = '8px';
    row.appendChild(cell);
}

for (let i = 1; i <= lastDate; i++) {
    if ((firstDay + i - 1) % 7 === 0) {
        calendar.appendChild(row);
        row = document.createElement('tr');
    }
    const cell = document.createElement('td');
    cell.addEventListener('mouseover', () => {
        cell.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.1)';
    });
    cell.addEventListener('mouseout', () => {
        cell.style.boxShadow = 'none';
    });
    cell.addEventListener('click', () => {
        presentSchedule(i);
    });
    const dateDiv = document.createElement('div');
    dateDiv.textContent = i;
    cell.appendChild(dateDiv);
    cell.style.cursor = 'pointer';
    cell.style.border = '1px solid #ddd';
    cell.style.padding = '18px';
    if (i === date) {
        cell.style.backgroundColor = '#0073e6';
        cell.style.color = '#fff';
    }
    if ((firstDay + i - 1) % 7 === 0 || (firstDay + i - 1) % 7 === 6) {
        cell.style.color = 'red';
    }
    row.appendChild(cell);
}
calendar.appendChild(row);

scheduleTable.appendChild(calendar);

// Updated PresentSchedule function
function presentSchedule(selectedDate) {
    const TodaysSchedule = document.createElement('table');
    TodaysSchedule.style.width = '100%';
    TodaysSchedule.style.borderCollapse = 'collapse';
    const headerRowSchedule = document.createElement('tr');
    const headerSchedule1 = document.createElement('th');
    headerSchedule1.textContent = year + '年' + (month + 1) + '月' + selectedDate + '日' + '(' + days[(new Date(year, month, selectedDate)).getDay()] + ')';
    headerSchedule1.style.border = '1px solid #ddd';
    headerSchedule1.style.padding = '8px';
    headerSchedule1.style.backgroundColor = '#f4f4f4';
    headerRowSchedule.appendChild(headerSchedule1);

    const headerSchedule2 = document.createElement('th');
    headerSchedule2.textContent = '授業';
    headerSchedule2.style.border = '1px solid #ddd';
    headerSchedule2.style.padding = '8px';
    headerSchedule2.style.backgroundColor = '#f4f4f4';
    headerRowSchedule.appendChild(headerSchedule2);

    TodaysSchedule.appendChild(headerRowSchedule);


    // Schedule times and subjects
    const SchoolTimes = [
        ['9:00~9:45', '9:55~10:40', '10:50~11:35', '11:45~12:30'],
        ['13:20~14:05', '14:15~15:00', '15:10~15:55', '16:05~16:50']
    ];

    // Subjects mapped to the time slots for each weekday
    const subjectsByDay = {
        0: ['休み', '休み', '休み', '休み', '休み', '休み', '休み', '休み'], // Sunday
        1: ['Python', 'Python', 'Html', 'Html', 'AI', 'AI', '総合日本語', '総合日本語'], // Monday
        2: ['Excel', 'Excel', 'Fusion', 'Fusion', 'Word', 'Word', 'キャリア', 'キャリア'], // Tuesday
        3: ['ビジネス日本語', 'ビジネス日本語', '卒業制作', '卒業制作', 'Python', 'Python', 'Html', 'Html'], // Wednesday
        4: ['AI', 'AI', '総合日本語', '総合日本語', 'Excel', 'Excel', 'Fusion', 'Fusion'], // Thursday
        5: ['Word', 'Word', 'キャリア', 'キャリア', 'ビジネス日本語', 'ビジネス日本語', '卒業制作', '卒業制作'], // Friday
        6: ['休み', '休み', '休み', '休み', '休み', '休み', '休み', '休み'] // Saturday
    };

    const selectedDay = new Date(year, month, selectedDate).getDay();
    const subjects = subjectsByDay[selectedDay];

    // Create rows for each time slot and display the corresponding subject
    for (let i = 0; i < SchoolTimes[0].length; i++) {
        const scheduleRow = document.createElement('tr');
        const timeCell = document.createElement('td');
        timeCell.textContent = SchoolTimes[0][i];
        timeCell.style.border = '1px solid #ddd';
        timeCell.style.padding = '8px';
        scheduleRow.appendChild(timeCell);

        const subjectCell = document.createElement('td');
        subjectCell.textContent = subjects[i]; // Get the subject for the slot
        subjectCell.style.border = '1px solid #ddd';
        subjectCell.style.padding = '8px';
        scheduleRow.appendChild(subjectCell);

        TodaysSchedule.appendChild(scheduleRow);
    }

    // Add the schedule to the page
    const scheduleContainer = document.getElementById('schedule-container');
    scheduleContainer.innerHTML = ''; // Clear previous content
    scheduleContainer.appendChild(TodaysSchedule);
}
const classroomFacilities = document.getElementById('classroomFacilities');
const facilities = ['Python', 'Html', 'AI', '総合日本語', 'Excel', 'Fusion', 'Word', 'キャリア', 'ビジネス日本語', '卒業制作'];
const classroomList = document.getElementById('classroomList');
const classroom = ["AP2-1", "AP2-2", "AP1-1", "AP1-2", "AP1-3", "AP1-4", "AP1-5"];

const classroomTable = document.createElement('table');
classroomTable.style.width = '100%';
classroomTable.style.borderCollapse = 'collapse';
    const headerRowClassroom = document.createElement('tr');
    const headerClassroom1 = document.createElement('th');
    headerClassroom1.textContent = '教室';
    headerClassroom1.style.border = '1px solid #ddd';
    headerClassroom1.style.padding = '8px';
    headerClassroom1.style.backgroundColor = '#f4f4f4';
    headerRowClassroom.appendChild(headerClassroom1);

    const headerClassroom2 = document.createElement('th');
    headerClassroom2.textContent = '設備';
    headerClassroom2.style.border = '1px solid #ddd';
    headerClassroom2.style.padding = '8px';
    headerClassroom2.style.backgroundColor = '#f4f4f4';
    headerRowClassroom.appendChild(headerClassroom2);

    classroomTable.appendChild(headerRowClassroom);

    for (let i = 0; i < classroom.length; i++) {
        const row = document.createElement('tr');
        const cellClassroom = document.createElement('td');
        cellClassroom.textContent = classroom[i];
        cellClassroom.style.border = '1px solid #ddd';
        cellClassroom.style.padding = '8px';
        row.appendChild(cellClassroom);

        const cellFacility = document.createElement('td');
        cellFacility.textContent = facilities[i % facilities.length];
        cellFacility.style.border = '1px solid #ddd';
        cellFacility.style.padding = '8px';
        row.appendChild(cellFacility);

        classroomTable.appendChild(row);
    }

    classroomFacilities.appendChild(classroomTable);
