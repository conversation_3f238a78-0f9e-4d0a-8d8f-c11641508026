<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>あずまホームページ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="static/styles.css">

</head>
<body>
    <header id="home">
        <button id="menuButton" onclick="toggleNav()"><i class="fas fa-bars" style="font-size: 24px; color: white;"></i></button>
        <div>
            <img src="../azuma_logo.png" alt="Azuma Logo">
        </div>
    </header>

    <div class="sidenav" id="mySidenav">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <a href="azumamain.html">ホーム</a>
        <a href="classroom.html">授業</a>
        <a href="Schedule.html">スケジュール</a>
        <a href="download.html">ダウンロード</a>
        <a href="consultation.html">相談</a>
        <a href="servicelists.html">サービス</a>
    </div>
    <nav class="navbar">
        <a href="#home">ホーム</a>
        <a href="#main">メイン</a>
        <a href="#contents">コンテンツ</a>
        <a href="#contact">コンタクト</a>
        <a href="download.html">ダウンロード</a>
    </nav>
    <div id="language-options">
        <div id="google_translate_element"></div>
        <script>
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: 'jp',
                    includedLanguages: 'en,ne,zh-CN,si,ja,bn,vi,my,mn,ru,zh',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                }, 'google_translate_element');
            }
        </script>
        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
    </div>
    <main>
        <section id="main">
                <h2>サービス一覧</h2>
                <p>
                    あずま工科専門学校では、入学案内、オープンキャンパス、学校見学、就職支援など、学生と保護者の皆様に向けた多彩なサービスを提供しています。
                </p>
                <h3>提供サービス</h3>
                <ul>
                    <li>入学案内</li>
                    <li>オープンキャンパス</li>
                    <li>学校見学</li>
                    <li>就職支援</li>
                    <li>個別相談</li>
                </ul>
                <img src="images/services.jpg" alt="サービス一覧" style="width: 80%;">

        </section>
    </main>
    <footer>
        <section>
            <h3 style="border-bottom: #ffffff inset 2px;">Information</h3>
            <br>
            <a href="#home"><img src="../azuma_logo.png" alt=""></a>
            <div id="contact">
                <address style="font-size: medium; font-style: normal; color: #fff;">
                    <div><strong>神奈川県横浜市中区初音町1-2-2</strong></div>
                    <div>Tel: ************</div>
                    <div>Fax: ************</div>
                    <div>Mail: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                    <div>（平日 9:00 ~ 17:00）</div>
                </address>
            </div>
            <br>
            <div id="contents">
                <h3 style="border-bottom: #ffffff inset 2px;">Contents</h3>
                <br>
                <nav>
                    <a href="OpenCampus.html">オーペンキャンパス</a><br>
                    <a href="admission.html">入学案内</a><br>
                    <a href="schooltour.html">学校ツアー</a><br>
                    <a href="placement.html">就職歴</a><br>
                </nav>
            </div>
            <br>
            <div>
                <p style="text-align: right; font-size: small">&copy; 2025 あずま工科専門学校</p>
            </div>
        </section>
    </footer>
    <button id="goToTop" onclick="scrollToTop()"><i class="fas fa-arrow-up"></i></button>
    <script>
        
        const menuButton = document.getElementById("menuButton");
        const sideNav = document.getElementById("mySidenav");

        function toggleNav() {
            sideNav.classList.toggle("open");
        }

        function closeNav() {
            sideNav.classList.remove("open");
        }

        document.addEventListener('click', function (event) {
            if (!sideNav.contains(event.target) && !menuButton.contains(event.target) && sideNav.classList.contains('open')) {
                closeNav();
            }
        });

        window.onscroll = function () {
            const goToTop = document.getElementById("goToTop");
            if (document.documentElement.scrollTop > 100) {
                goToTop.classList.add("show");
            } else {
                goToTop.classList.remove("show");
            }
        };

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>