<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            height: 100vh;
            margin: 0;
        }
        .header {
            background-color: #f1f1f1;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            flex: 1;
        }
        .main {
            flex: 3;
            padding: 20px;
        }
        .sidebar {
            flex: 1;
            background-color: #f9f9f9;
            padding: 20px;
        }
        .search-results {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
        }
        .search-results > div {
            background-color: #e1e1e1;
            margin: 5px;
            padding: 10px;
            flex: 1 1 calc(50% - 10px); /* Adjust the width as needed */
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Header</h1>
    </div>
    <div class="content">
        <div class="main">
            <h2>Main Content</h2>
            <p>This is the main content area.</p>
        </div>
        <div class="sidebar">
            <h2>Search</h2>
            <input type="text" id="searchInput" placeholder="Search...">
            <button onclick="performSearch()">Search</button>
            <div class="search-results" id="searchResults">
                <div id="resultNo">
                    <h3>Search Results</h3>
                    <p>No results found.</p>
                </div>
            </div>
        </div>
    </div>
    <script>
        let searchCount = 0;

        function performSearch() {
            if (searchCount < 5) {
            var searchResults = document.getElementById('searchResults');
            searchResults.style.display = 'flex';
            if (searchCount === 0) {
                searchResults.innerHTML = '';
            }

            var newResult = document.createElement('div');
            newResult.id = 'resultNo' + (searchCount + 1);
            newResult.innerHTML = '<h3>Search Results ' + (searchCount + 1) + '</h3><p>No results found.</p><button onclick="deleteResult(\'resultNo' + (searchCount + 1) + '\')">✖</button>';
            searchResults.appendChild(newResult);

            searchCount++;
            } else {
            alert('You have reached the maximum number of searches.');
            }
        }

        function deleteResult(resultId) {
            var result = document.getElementById(resultId);
            if (result) {
            result.remove();
            searchCount--;
            }
        }
    </script>
</body>
</html>