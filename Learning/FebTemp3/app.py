from flask import Flask, jsonify, request, render_template, Response
from flask_cors import CORS
from mysql import connector
import json
import oauthReq as oauth

app = Flask(__name__)
CORS(app)

def get_connection():
    return connector.connect(host='localhost', user='admin', password='password', database='poidb')

@app.route('/')
def index():
    api_key = oauth.getUrl()
    return render_template('index.html', api_key=api_key)

@app.route('/search', methods=['GET'])
def search():
    search = request.args.get('keywords', '')
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    if search:
        cursor.execute("""
            SELECT * FROM poitbl 
            WHERE MATCH(Name, Branch, Address) AGAINST(%s IN NATURAL LANGUAGE MODE)
        """, (search,))
    else:
        cursor.execute("SELECT * FROM poitbl")

    results = cursor.fetchall()
    conn.close()
    return jsonify(results)

@app.route('/get_markers', methods=['GET'])
def get_markers():
    """Streams all marker data for clustering."""
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)
    cursor.execute("SELECT ID, Name, Branch, Latitude, Longitude FROM search_view;")

    def stream_results():
        for row in cursor:
            yield json.dumps(row) + "\n"

    return Response(stream_results(), content_type='application/json')

if __name__ == '__main__':
    app.run(debug=True, host='localhost', port=5000)
