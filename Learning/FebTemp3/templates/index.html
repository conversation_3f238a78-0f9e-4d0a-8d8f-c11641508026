<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="{{ api_key }}"></script>  <!-- Ensure Flask replaces this -->
    <script src="../static/functions.js"></script>
    <link rel="stylesheet" href="../static/styles.css">
    <title>Temp3</title>
</head>
<body>
    <div id="searchbox">
        <input type="text" id="search" placeholder="Input Keywords..">
        <button id="searchBtn">Search</button>
    </div>
    <div id="toggleBtn">▼ Show Results (<span id="resultCount">0</span>)</div>
    <div id="results">
        <div id="result-item"></div>
    </div>
</body>
<script>
    const search = document.getElementById('search');
    const searchBtn = document.getElementById('searchBtn');
    const toggleBtn = document.getElementById('toggleBtn');
    const resultCount = document.getElementById('resultCount');
    const results = document.getElementById('results');
    const resultItem = document.getElementById('result-item');

    searchBtn.addEventListener('click', () => {
        const keywords = search.value.trim();
        if (!keywords) return;  // Prevent empty searches

        fetch(`http://localhost:5000/search?keywords=${encodeURIComponent(keywords)}`)
            .then(response => response.json())
            .then(data => {
                resultItem.innerHTML = '';
                resultCount.textContent = data.length;
                data.forEach(item => {
                    const div = document.createElement('div');
                    div.innerHTML = `
                        <h3>${item.Name} ${item.Branch}</h3>
                        <p>${item.Address}</p>
                    `;
                    resultItem.appendChild(div);
                });
            })
            .catch(error => console.error("Error fetching search results:", error));
    });

    function loadMarkers() {
        fetch("http://localhost:5000/get_markers")
            .then(response => response.text())
            .then(data => {
                const markers = data.trim().split("\n").map(line => JSON.parse(line));
                console.log("Markers Loaded:", markers);
                addMarkersToMap(markers);
            })
            .catch(error => console.error("Error loading markers:", error));
    }

    function addMarkersToMap(markers) {
        if (!markers.length) {
            console.warn("No markers found.");
            return;
        }
        console.log("Adding markers to map:", markers);
        // Call your actual marker clustering function here
    }

    loadMarkers();  // Load markers on page load
</script>
</html>
